{"pages": ["pages/home/<USER>", "pages/content/content", "pages/content/article", "pages/content/video", "pages/content/picture", "pages/user/profile", "pages/contact/contact", "pages/user/bind", "pages/content/champion", "pages/content/pointrank", "pages/content/rungroup", "pages/starphoto/list", "pages/starphoto/photo", "pages/starphoto/comment", "pages/user/qrcode", "pages/user/info", "pages/user/verify", "pages/user/advancedverify", "pages/user/result", "pages/user/resultclaim", "pages/user/myphoto", "pages/user/myphotodesc", "pages/user/roadmap", "pages/user/regist", "pages/starphoto/activity", "pages/activity/activitySignUp/SignUpIndex/SignUpIndex", "pages/activity/activitySignUp/activitySignUpTip/activitySignUpTip", "pages/activity/activitySignUp/mySignUp/mySignUp", "pages/activity/activitySignUp/mySignUp/payTips", "pages/activity/activityPublic/publicDraw", "pages/activity/activityList/activityList1", "pages/user/newroadmap", "pages/activity/activitySchool/activitySchoolSignUp", "pages/activity/activitySchool/viewSignUpInfo", "pages/activity/activityGoods/activityGoods", "pages/activity/activityGoodsDetail/activityGoodsDetail", "pages/activity/activityUserOrder/activityUserOrder", "pages/activity/activityGoodsBuy/activityGoodsBuy", "pages/activity/activityGoodsAddress/activityGoodsAddress", "pages/activity/activityGoodsAddressSave/activityGoodsAddressSave", "pages/activity/activityList/activityList2", "pages/activity/activityList/publicpage", "pages/activity/activityPublic/publicWelfare", "pages/user/certificate/index", "pages/user/certificate/list", "pages/user/certificate/detail", "pages/events/index", "pages/events/list", "pages/events/calendar", "pages/events/registration/index", "pages/events/registration/detail/index", "pages/events/registration/list", "pages/events/registration/success", "pages/events/registration/fail", "pages/events/registration/agreement/index", "pages/user/orders/detail", "pages/training/index", "pages/training/plans", "pages/training/plan-detail", "pages/training/run/record", "pages/training/run/result", "pages/training/run/history", "pages/community/index", "pages/community/post", "pages/community/publish", "pages/community/running-teams/index", "pages/community/running-teams/detail", "pages/ranking/index", "pages/message/index", "pages/search/index", "pages/news/index", "pages/news/detail", "pages/topics/emergency-training", "pages/topics/charity", "pages/topics/charity-detail"], "usingComponents": {"van-icon": "@vant/weapp/icon/index", "van-button": "@vant/weapp/button/index", "van-cell": "@vant/weapp/cell/index", "van-cell-group": "@vant/weapp/cell-group/index", "van-tag": "@vant/weapp/tag/index", "van-notice-bar": "@vant/weapp/notice-bar/index", "van-loading": "@vant/weapp/loading/index", "van-dialog": "@vant/weapp/dialog/index", "van-overlay": "@vant/weapp/overlay/index", "van-toast": "@vant/weapp/toast/index"}, "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#a5191e", "navigationBarTitleText": "中国马拉松大满贯", "navigationBarTextStyle": "white"}, "tabBar": {"custom": false, "color": "#000000", "selectedColor": "#a5191e", "list": [{"pagePath": "pages/home/<USER>", "iconPath": "./image/home.png", "selectedIconPath": "./image/home_active.png", "text": "主页"}, {"pagePath": "pages/starphoto/activity", "iconPath": "./image/activity.png", "selectedIconPath": "./image/activity_active.png", "text": "活动"}, {"pagePath": "pages/activity/activityGoods/activityGoods", "iconPath": "./image/mall_noselect.png", "selectedIconPath": "./image/mall_select.png", "text": "商城"}, {"pagePath": "pages/user/profile", "iconPath": "./image/user.png", "selectedIconPath": "./image/user_active.png", "text": "我的"}]}, "sitemapLocation": "sitemap.json", "lazyCodeLoading": "requiredComponents", "permission": {"scope.userLocation": {"desc": "您的位置信息将用于记录跑步轨迹"}}, "requiredBackgroundModes": ["location"]}