/* 页面基础样式 */
page {
  /* 主色调 - 马拉松红色系 */
  --primary-color: #a5191e;
  --primary-light: #f8e8e8;
  --primary-dark: #800000;
  --primary-gradient: linear-gradient(135deg, #a5191e, #800000);
  
  /* 辅助色 - 活力橙色系 */
  --accent-color: #ff8f1f;
  --accent-light: #ffebcc;
  --accent-gradient: linear-gradient(135deg, #ff8f1f, #ff6b00);
  
  /* 功能色 */
  --success-color: #25b864;
  --success-light: #e6f7ee;
  --info-color: #2d8cf0;
  --info-light: #e8f4ff;
  --warning-color: #ffb020;
  --warning-light: #fff8e6;
  
  /* 文本色 */
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --text-white: #ffffff;
  
  /* 背景与边框 */
  --border-color: #eeeeee;
  --bg-color: #f7f8fa;
  --card-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --primary-shadow: 0 4rpx 16rpx rgba(165, 25, 30, 0.2);
  --accent-shadow: 0 4rpx 16rpx rgba(255, 143, 31, 0.2);

  /* 动画时间 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* 响应式设计变量 */
  --page-padding: 24rpx;
  --card-border-radius: 16rpx;
  --header-height: 88rpx;
  --safe-area-bottom: env(safe-area-inset-bottom, 0);

  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-color);
  background-color: #f7f8fa;
  box-sizing: border-box;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.05) 0%, rgba(212, 40, 46, 0.02) 100%);
  z-index: -1;
}

page::after {
  content: '';
  position: fixed;
  top: 30%;
  right: -10%;
  width: 600rpx;
  height: 600rpx;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(165, 25, 30, 0.03) 0%, rgba(165, 25, 30, 0.01) 50%, transparent 70%);
  z-index: -1;
}

/* 适配不同设备 */
@media screen and (min-width: 768px) {
  page {
    --page-padding: 32rpx;
    --card-border-radius: 20rpx;
  }
}

.marathon-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 顶部导航栏 - 高端玻璃风格 */
.top-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.18);
}

.top-header.scrolled {
  background: rgba(255, 255, 255, 0.98);
  padding: 12rpx 24rpx;
}

.new-header {
  animation: slideDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.logo {
  width: 64rpx;
  height: 64rpx;
  margin-right: 20rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(165, 25, 30, 0.2);
  transition: all 0.3s ease;
  animation: pulse 2s infinite alternate;
}

.logo:active {
  transform: scale(0.9) rotate(5deg);
}

@keyframes pulse {
  0% {
    box-shadow: 0 4rpx 12rpx rgba(165, 25, 30, 0.2);
  }
  100% {
    box-shadow: 0 4rpx 20rpx rgba(165, 25, 30, 0.4);
  }
}

/* 搜索栏 - 高端风格 */
.search-bar-inline {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(247, 248, 250, 0.8);
  border-radius: 40rpx;
  padding: 16rpx 28rpx;
  margin: 0 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06), inset 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.search-bar-inline::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: rotate(45deg);
  transition: all 0.5s ease;
  opacity: 0;
}

.search-bar-inline:active {
  transform: translateY(2rpx);
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3rpx var(--primary-light), 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.search-bar-inline:active::before {
  animation: searchShimmer 1s forwards;
  opacity: 1;
}

@keyframes searchShimmer {
  0% {
    transform: translateX(-150%) translateY(-150%) rotate(45deg);
  }
  100% {
    transform: translateX(150%) translateY(150%) rotate(45deg);
  }
}

.search-input-inline {
  flex: 1;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: var(--text-color);
  margin-left: 16rpx;
  position: relative;
  z-index: 2;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

.search-input-inline::placeholder {
  color: var(--text-light);
  font-weight: 400;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.search-input-inline:focus::placeholder {
  opacity: 0.5;
  transform: translateX(5rpx);
}

.search-placeholder {
  flex: 1;
  font-size: 28rpx;
  color: var(--text-light);
  margin-left: 16rpx;
  font-weight: 400;
  opacity: 0.8;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  letter-spacing: 0.5rpx;
}

.search-bar-inline:active .search-placeholder {
  opacity: 0.6;
  transform: translateX(5rpx);
}

/* 头部操作区 - 高端风格 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 32rpx;
  position: relative;
}

.header-actions::before {
  content: '';
  position: absolute;
  left: -20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.05), transparent);
}

.action-icon {
  position: relative;
  transition: all 0.3s ease;
  transform-origin: center;
}

.action-icon:active {
  transform: scale(0.85);
  color: var(--primary-color) !important;
}

.action-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(165, 25, 30, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.4s ease-out;
  z-index: -1;
  opacity: 0;
}

.action-icon:active::after {
  width: 200%;
  height: 200%;
  opacity: 1;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 36rpx;
  height: 36rpx;
  line-height: 36rpx;
  text-align: center;
  background: linear-gradient(135deg, #a5191e, #800000);
  color: var(--text-white);
  border-radius: 18rpx;
  font-size: 20rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(165, 25, 30, 0.4);
  animation: badgePulse 2s infinite;
  padding: 0 8rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  transform-origin: center;
  z-index: 2;
}

@keyframes badgePulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(165, 25, 30, 0.4);
  }
  50% {
    transform: scale(1.15);
    box-shadow: 0 4rpx 20rpx rgba(165, 25, 30, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 12rpx rgba(165, 25, 30, 0.4);
  }
}

/* 内容区域 - 高端滚动效果 */
.content-scroll {
  height: calc(100vh - 88rpx);
  margin-top: 88rpx;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  padding-bottom: 40rpx;
  position: relative;
}

.content-scroll::after {
  content: '';
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(to top, rgba(247, 248, 250, 0.9), transparent);
  pointer-events: none;
  z-index: 5;
}

/* 轮播图区域 - 高端3D效果 */
.banner-container {
  width: 100%;
  height: 340rpx;
  background: linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(247,248,250,0.8) 100%);
  overflow: visible;
  padding: 0 24rpx;
  box-sizing: border-box;
  margin-top: 40rpx;
  margin-bottom: 30rpx;
  position: relative;
}

.banner-container::before {
  content: '';
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 340rpx;
  background: radial-gradient(ellipse at center, rgba(165, 25, 30, 0.15), transparent 70%);
  filter: blur(20rpx);
  z-index: 0;
  opacity: 0.6;
}

.banner-container .swiper-item {
  border-radius: 20rpx;
  overflow: hidden;
  transform: scale(0.92) translateY(0);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.banner-container .swiper-item.active {
  transform: scale(1) translateY(-10rpx);
  box-shadow: 0 20rpx 50rpx rgba(0, 0, 0, 0.15);
}

.banner-container .swiper-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60%;
  background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
  z-index: 1;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.banner-container .swiper-item:active::after {
  opacity: 0.6;
}

.banner-swiper {
  width: 100%;
  height: 340rpx;
  overflow: visible;
}

.banner-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  transition: transform 0.5s ease;
}

.swiper-item:active .banner-image {
  transform: scale(1.05);
}

.banner-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 24rpx;
  color: #fff;
  font-size: 30rpx;
  font-weight: 600;
  z-index: 2;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.5);
  border-bottom-left-radius: 20rpx;
  border-bottom-right-radius: 20rpx;
  letter-spacing: 1rpx;
  transition: all 0.3s ease;
}

.swiper-item:active .banner-title {
  padding-bottom: 36rpx;
  transform: translateY(-4rpx);
}

/* 公告栏 - 高端风格 */
.notice-container {
  margin: 20rpx 24rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  background: linear-gradient(120deg, rgba(255,255,255,0.9), rgba(248,232,232,0.9));
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.notice-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(to right, transparent, rgba(165, 25, 30, 0.2), transparent);
}

.notice-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(to right, transparent, rgba(165, 25, 30, 0.2), transparent);
}

/* 入口网格 - 高端风格 */
.entry-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 24rpx;
  padding: 30rpx 24rpx;
  background: linear-gradient(135deg, #ffffff, #f8f9fc);
  margin: 0 24rpx 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.entry-grid::before {
  content: '';
  position: absolute;
  top: -10%;
  left: -10%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle at top right, rgba(165, 25, 30, 0.05), transparent 70%);
  z-index: -1;
}

.entry-grid::after {
  content: '';
  position: absolute;
  bottom: -10%;
  right: -10%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle at bottom left, rgba(255, 143, 31, 0.05), transparent 70%);
  z-index: -1;
}

.entry-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 16rpx 0;
  position: relative;
}

.entry-item:active {
  transform: scale(0.92) translateY(4rpx);
}

.entry-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3rpx;
  background: var(--primary-gradient);
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
  border-radius: 3rpx;
}

.entry-item:active::after {
  width: 60%;
  opacity: 1;
}

.entry-icon-img {
  width: 96rpx;
  height: 96rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-gradient);
  box-shadow: var(--primary-shadow);
  color: var(--text-white);
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
  transform: rotate(0deg);
}

.entry-icon-img::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(45deg);
  animation: shimmer 3s infinite;
  z-index: 1;
}

.entry-item:active .entry-icon-img {
  border-radius: 50%;
  transform: rotate(-15deg);
}

@keyframes shimmer {
  0% {
    transform: translateX(-150%) translateY(-150%) rotate(45deg);
  }
  100% {
    transform: translateX(150%) translateY(150%) rotate(45deg);
  }
}

.modern-icon-img {
  width: 48rpx;
  height: 48rpx;
  z-index: 2;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
}

.modern-icon-img {
  width: 48rpx;
  height: 48rpx;
}

.entry-label {
  font-size: 24rpx;
  color: var(--text-color);
  font-weight: 600;
  letter-spacing: 0.5rpx;
  position: relative;
  padding: 0 4rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.entry-item:active .entry-label {
  color: var(--primary-color);
  transform: translateY(-2rpx);
}

/* 背景色类 - 高端渐变风格 */
.bg-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.bg-secondary {
  background: linear-gradient(135deg, #800000, #5d0000);
}

.bg-accent {
  background: linear-gradient(135deg, var(--accent-color), #e67300);
}

.bg-success {
  background: linear-gradient(135deg, var(--success-color), #1a8047);
}

.bg-info {
  background: linear-gradient(135deg, var(--info-color), #1a6fc7);
}

/* 分隔线 */
.divider {
  height: 1rpx;
  background: linear-gradient(to right, transparent, var(--border-color), transparent);
  margin: 24rpx 0;
  opacity: 0.8;
}

/* 内容区块 - 高端设计 */
.section-container {
  margin: 30rpx 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.06);
  position: relative;
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

.section-container:active {
  transform: translateY(-5rpx);
  box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.1);
}

.section-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(to right, rgba(165, 25, 30, 0.6), rgba(165, 25, 30, 0.2));
}

/* 区块头部 - 高端设计 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 28rpx 24rpx;
  position: relative;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.8), rgba(248, 248, 248, 0.8));
  z-index: 1;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 10%;
  right: 10%;
  height: 1rpx;
  background: linear-gradient(to right, transparent, rgba(165, 25, 30, 0.15), transparent);
}

.section-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  position: relative;
  padding-left: 20rpx;
  letter-spacing: 1rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 32rpx;
  background: linear-gradient(to bottom, #a5191e, #d4282e);
  border-radius: 4rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6rpx;
  height: 32rpx;
  background: var(--primary-gradient);
  border-radius: 3rpx;
}

.title-icon {
  width: 6rpx;
  height: 32rpx;
  background-color: var(--primary-color);
  margin-right: 16rpx;
  border-radius: 3rpx;
  box-shadow: 0 0 6rpx rgba(165, 25, 30, 0.3);
}

.section-more {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.6);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.section-more:active {
  background: rgba(165, 25, 30, 0.1);
  transform: translateX(5rpx);
}

.more-link:active {
  background-color: rgba(0, 0, 0, 0.05);
}

.more-link text {
  margin-right: 4rpx;
}

/* 热门赛事 - 高端设计 */
.hot-races {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding: 10rpx 5rpx;
}

.hot-races race-card {
  transition: all 0.3s ease;
  transform-origin: center;
}

.hot-races race-card:active {
  transform: scale(0.98);
}

.race-card {
  height: 200rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  animation: slideUp 0.5s ease-out;
}

.race-card.modern {
  transform: translateZ(0);
}

.race-card.modern:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.race-card-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.race-card:hover .race-card-bg {
  transform: scale(1.05);
}

.race-card-overlay {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4));
}

.race-card-content {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 28rpx 32rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.race-card-title-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.race-card-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  max-width: 70%;
  line-height: 1.3;
}

.race-card-countdown {
  background: linear-gradient(135deg, #ff8f1f, #f57c00);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  padding: 6rpx 20rpx;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 143, 31, 0.4);
}

.race-card-info {
  display: flex;
  flex-direction: column;
  gap: 14rpx;
}

.race-card-date, .race-card-location {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.95);
  font-size: 26rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.race-card-date text, .race-card-location text {
  margin-left: 10rpx;
}

.race-card-status {
  position: absolute;
  right: 28rpx;
  bottom: 28rpx;
}

.race-card-event-signup-btn {
  background: linear-gradient(135deg, #a5191e, #c62828);
  color: #fff;
  border-radius: 28rpx;
  padding: 0 28rpx;
  height: 56rpx;
  line-height: 56rpx;
  font-size: 26rpx;
  font-weight: bold;
  border: none;
  box-shadow: 0 6rpx 12rpx rgba(165, 25, 30, 0.4);
  transition: all 0.3s ease;
}

.race-card-event-signup-btn:active {
  transform: scale(0.95);
  box-shadow: 0 3rpx 8rpx rgba(165, 25, 30, 0.3);
  background: linear-gradient(135deg, #941619, #b32323);
}

/* 资讯中心 - 现代化设计 */
.news-section {
  margin-bottom: 30rpx;
}

.news-header {
  margin-bottom: 24rpx;
}

.news-icon {
  background-color: #e74c3c;
}

.news-more {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 资讯分类标签 */
.news-tabs {
  display: flex;
  padding: 0 20rpx;
  margin-bottom: 28rpx;
  overflow-x: auto;
  white-space: nowrap;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.news-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.news-tab {
  padding: 10rpx 28rpx;
  margin-right: 20rpx;
  font-size: 26rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  transition: all 0.3s ease;
}

.news-tab:active {
  opacity: 0.8;
  transform: scale(0.96);
}

.news-tab.active {
  color: #fff;
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(231, 76, 60, 0.3);
}

/* 焦点资讯 */
.featured-news {
  position: relative;
  margin: 0 20rpx 28rpx;
  height: 380rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.5s ease-out;
}

.featured-news-image {
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease;
}

.featured-news:active .featured-news-image {
  transform: scale(1.05);
}

.featured-news-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 75%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85), transparent);
}

.featured-news-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 36rpx;
  z-index: 1;
}

.featured-news-category {
  display: inline-block;
  padding: 8rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #fff;
  border-radius: 6rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
}

.featured-news-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1.4;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.4);
}

.featured-news-meta {
  display: flex;
  align-items: center;
}

.featured-news-time {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
}

.featured-news-time text {
  margin-left: 8rpx;
}

/* 资讯列表 */
.news-list {
  padding: 0 20rpx;
}

.news-item {
  display: flex;
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  animation: slideUp 0.5s ease-out;
}

.news-item:active {
  transform: translateY(4rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.06);
  background-color: #fafafa;
}

.news-item-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
  background-color: #f5f5f5;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.news-item-content {
  flex: 1;
  margin-left: 24rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-item-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: auto;
}

.news-item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.news-item-category {
  padding: 4rpx 16rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: #fff;
  border-radius: 6rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
}

.news-item-time {
  display: flex;
  align-items: center;
  font-size: 22rpx;
  color: #999;
}

.news-item-time text {
  margin-left: 6rpx;
}

/* 空状态 */
.news-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  color: #999;
  font-size: 28rpx;
}

.news-empty text {
  margin-top: 24rpx;
}

/* 精选专题 - 优化后 */
.featured-banners {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

.featured-banner {
  height: 160rpx;
  border-radius: 18rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  animation: slideUp 0.5s ease-out;
}

.featured-banner.modern:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.featured-banner image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.featured-banner:hover image {
  transform: scale(1.05);
}

.featured-banner-text {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 30rpx 36rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.75), rgba(0, 0, 0, 0.2));
}

.featured-banner-title {
  color: white;
  font-size: 34rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.featured-banner-desc {
  color: rgba(255, 255, 255, 0.95);
  font-size: 26rpx;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

/* 训练指南卡片样式 */
.training-section {
  margin-bottom: 20rpx;
}

.training-cards {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 20rpx;
  gap: 20rpx;
}

.training-card {
  width: 300rpx;
  min-width: 300rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.training-card.modern {
  border: none;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.training-card.modern:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
}

.training-card-header {
  padding: 16rpx 20rpx;
  color: #fff;
  display: flex;
  align-items: center;
  gap: 10rpx;
  font-weight: bold;
}

.training-card-header.beginner {
  background-color: #4CAF50;
}

.training-card-header.intermediate {
  background-color: #2196F3;
}

.training-card-header.advanced {
  background-color: #FF9800;
}

.training-card-header.elite {
  background-color: #E91E63;
}

.training-card-header.special {
  background-color: #9C27B0;
}

.training-card-body {
  padding: 16rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.training-detail-item {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  font-size: 24rpx;
  color: #333;
  line-height: 1.4;
}

.training-detail-item van-icon {
  margin-top: 4rpx;
}

.section-content {
  padding: 30rpx 24rpx;
  position: relative;
  z-index: 0;
}

.section-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(255, 255, 255, 0.9), transparent);
  z-index: -1;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.title-icon {
  width: 6rpx;
  height: 30rpx;
  background-color: #a5191e;
  margin-right: 16rpx;
  border-radius: 3rpx;
}

.more-link {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.more-link text {
  margin-right: 6rpx;
}

/* 社区动态 - 优化后 */
.community-posts {
  display: flex;
  flex-direction: column;
  gap: 28rpx;
}

/* 精选话题 - 高端设计 */
.featured-topics {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
  padding: 10rpx 5rpx;
  /* 确保没有限制显示数量的样式 */
  max-height: none;
  overflow: visible;
}

.featured-topics community-post {
  position: relative;
  transition: all 0.3s ease;
  transform-origin: center;
}

.featured-topics community-post:active {
  transform: scale(0.98);
}

.featured-topics community-post::before {
  content: '';
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #a5191e, #d4282e);
  opacity: 0;
  animation: pulseDot 2s infinite;
  z-index: 10;
}

@keyframes pulseDot {
  0% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
}

.community-post {
  padding: 28rpx;
  border-radius: 18rpx;
  background-color: #f9fafb;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: slideUp 0.5s ease-out;
}

.community-post::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background: linear-gradient(to right, #a5191e, #ff8f1f);
}

.community-post.modern:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 10rpx rgba(0, 0, 0, 0.06);
}

.post-user {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.user-avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  border: 2rpx solid #fff;
  transition: transform 0.3s ease;
}

.user-avatar:active {
  transform: scale(0.95);
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 6rpx;
}

.post-time {
  font-size: 24rpx;
  color: var(--text-light);
}

.post-content {
  font-size: 28rpx;
  line-height: 1.6;
  margin-bottom: 24rpx;
  word-break: break-all;
  color: var(--text-color);
  padding: 0 4rpx;
}

.post-actions {
  display: flex;
  border-top: 1rpx solid var(--border-color);
  padding-top: 20rpx;
}

.action-item {
  display: flex;
  align-items: center;
  margin-right: 48rpx;
  font-size: 26rpx;
  color: var(--text-light);
  transition: all 0.2s ease;
  padding: 6rpx 12rpx;
  border-radius: 24rpx;
}

.action-item:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.96);
}

.action-item text {
  margin-left: 8rpx;
}

.liked-text {
  color: var(--primary-color);
  font-weight: 500;
}

/* 加载更多状态 - 优化后 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 36rpx 0;
  color: var(--text-light);
  font-size: 26rpx;
  gap: 16rpx;
}

/* 底部 - 优化后 */
.footer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 70rpx 0;
  gap: 16rpx;
  background-color: #fff;
  margin-top: 30rpx;
  border-top: 1rpx solid var(--border-color);
  position: relative;
}

.footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 200rpx;
  height: 4rpx;
  background: linear-gradient(to right, #a5191e, #ff8f1f);
}

.footer.modern {
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.footer-logo {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  transition: transform 0.3s ease;
}

.footer-logo:active {
  transform: scale(0.92);
}

.footer-text {
  font-size: 26rpx;
  color: var(--text-light);
  margin-bottom: 14rpx;
  font-weight: 500;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 12rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
}

.footer-links text {
  padding: 6rpx 12rpx;
  transition: all 0.3s ease;
}

.footer-links text:active {
  opacity: 0.7;
  transform: scale(0.95);
}

.divider-dot {
  color: var(--text-light);
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 高端动画定义 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInLeft {
  from { transform: translateX(-50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes scaleIn {
  0% { transform: scale(0.9); opacity: 0; }
  70% { transform: scale(1.03); opacity: 0.9; }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes floatUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes rotateIn {
  0% { transform: rotate(-10deg) scale(0.9); opacity: 0; }
  100% { transform: rotate(0) scale(1); opacity: 1; }
}

@keyframes slideUpFadeIn {
  0% { transform: translateY(30px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 高端动画应用 */
.banner-container {
  animation: slideUpFadeIn 1s cubic-bezier(0.165, 0.84, 0.44, 1) 0.1s both;
}

.notice-container {
  animation: slideInRight 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0.3s both;
}

.entry-grid {
  animation: fadeIn 1s ease-out 0.5s both;
}

.section-container:nth-child(1) {
  animation: slideInLeft 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0.7s both;
}

.section-container:nth-child(2) {
  animation: slideInRight 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0.9s both;
}

.entry-item:nth-child(1) {
  animation: scaleIn 0.7s cubic-bezier(0.165, 0.84, 0.44, 1) 0.6s both;
}

.entry-item:nth-child(2) {
  animation: scaleIn 0.7s cubic-bezier(0.165, 0.84, 0.44, 1) 0.7s both;
}

.entry-item:nth-child(3) {
  animation: scaleIn 0.7s cubic-bezier(0.165, 0.84, 0.44, 1) 0.8s both;
}

.entry-item:nth-child(4) {
  animation: scaleIn 0.7s cubic-bezier(0.165, 0.84, 0.44, 1) 0.9s both;
}

.entry-item:nth-child(5) {
  animation: scaleIn 0.7s cubic-bezier(0.165, 0.84, 0.44, 1) 1s both;
}

.hot-races race-card:nth-child(1) {
  animation: slideInLeft 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0.8s both;
}

.hot-races race-card:nth-child(2) {
  animation: slideInLeft 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 0.9s both;
}

.hot-races race-card:nth-child(3) {
  animation: slideInLeft 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 1s both;
}

.featured-topics community-post:nth-child(1) {
  animation: slideInRight 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 1.1s both;
}

.featured-topics community-post:nth-child(2) {
  animation: slideInRight 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 1.2s both;
}

.featured-topics community-post:nth-child(3) {
  animation: slideInRight 0.8s cubic-bezier(0.165, 0.84, 0.44, 1) 1.3s both;
}

/* pages/home/<USER>/

/* 排行榜模块样式 */
.leaderboard-section {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding-bottom: 20rpx;
}

.leaderboard-section .section-header {
  padding: 30rpx 30rpx 20rpx;
}

.leaderboard-section .section-title text {
  font-weight: bold;
}

.leaderboard-section .van-tabs__nav {
  background-color: transparent;
}
.leaderboard-section .van-tab {
  font-size: 28rpx;
}
.leaderboard-section .van-tab--active {
  font-weight: bold;
}

.group-tabs {
  margin-top: 10rpx;
  padding: 0 20rpx;
}

.group-tabs .van-tabs__nav--card {
  border-radius: 8rpx;
  border: 1px solid #a5191e;
  height: 64rpx;
}

.group-tabs .van-tab--card {
  border-color: #a5191e;
  line-height: 62rpx;
  font-size: 26rpx;
}

.group-tabs .van-tab--active.van-tab--card {
  background-color: #a5191e;
  color: #fff !important;
}

.leaderboard-group-content {
  padding: 20rpx 30rpx;
  min-height: 300rpx;
}

.champions-grid {
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.champion-card {
  background-color: #fdf6f6;
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: calc(50% - 10rpx);
  box-shadow: 0 2rpx 8rpx rgba(165, 25, 30, 0.1);
  position: relative;
  overflow: hidden;
}
.champion-card.small {
  padding: 15rpx;
  width: calc(50% - 10rpx);
}

.champion-avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  border: 2rpx solid #a5191e;
  margin-bottom: 10rpx;
}
.champion-avatar.small {
  width: 80rpx;
  height: 80rpx;
}

.champion-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.champion-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}
.champion-name.small {
  font-size: 26rpx;
}

.champion-gender-points {
  font-size: 22rpx;
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.champion-gender-points text {
   line-height: 1.4;
}
.champion-gender-points.small {
  font-size: 20rpx;
}
.champion-gender-points .points {
  color: #a5191e;
  font-weight: bold;
  margin-top: 4rpx;
}

.champion-badge {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
}
.champion-badge.small {
  width: 30rpx;
  height: 30rpx;
}

.other-ranked-title {
  font-size: 26rpx;
  color: #555;
  margin-bottom: 15rpx;
  padding-left: 10rpx;
  border-left: 6rpx solid #a5191e;
  line-height: 1;
}

.ranked-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.ranked-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 10rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.ranked-item-left {
  display: flex;
  align-items: center;
}

.ranked-name {
  color: #333;
  font-weight: 500;
}

.ranked-gender {
  color: #777;
  margin-left: 10rpx;
  font-size: 22rpx;
}

.ranked-points {
  color: #a5191e;
  font-weight: bold;
}

.public-group .van-collapse-item .van-cell {
  background-color: #fcfcfc;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}
.public-group .van-collapse-item .van-cell__title {
  font-weight: 500;
  font-size: 28rpx;
}
.public-group .van-collapse-item__content {
  padding: 20rpx 10rpx 10rpx;
  background-color: #fff;
}

.age-group-champions {
  margin-bottom: 20rpx;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 26rpx;
}
.empty-data van-icon {
  margin-bottom: 10rpx;
}
.empty-data.small-empty {
  padding: 20rpx 0;
  font-size: 24rpx;
}
.empty-data.small-empty van-icon {
  font-size: 24px !important;
}
.empty-data.small-empty text {
  margin-top: 8rpx;
}

.van-tabs__content {
  min-height: 200rpx;
}

/* 排行榜入口卡片 */
.ranking-entry-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg, #fff8f8, #ffebeb);
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(165, 25, 30, 0.1);
  transition: all 0.3s ease;
}

.ranking-entry-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(165, 25, 30, 0.08);
}

.ranking-entry-content {
  flex: 1;
}

.ranking-entry-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.ranking-entry-desc {
  font-size: 26rpx;
  color: #666;
}

.ranking-entry-image {
  width: 80rpx;
  height: 80rpx;
  margin-left: 20rpx;
}

/* 添加备用轮播图的样式 */
.direct-swiper {
  width: 100%;
  height: 380rpx;
}

.direct-swiper .banner-swiper {
  width: 100%;
  height: 340rpx;
  overflow: visible;
}

.direct-swiper .banner-image {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  transition: transform 0.5s ease;
}

.swiper-item:active .banner-image {
  transform: scale(1.05);
}

.direct-swiper .banner-title {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: bold;
  padding: 10rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 应用动画 */
.section-container {
  animation: fadeIn 0.5s ease-out;
}

.race-card, .training-card, .community-post, .news-item, .featured-banner {
  animation: slideUp 0.5s ease-out;
}

.badge {
  animation: pulse 2s infinite;
}

/* 加载状态 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #f8f8f8 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 8rpx;
}

/* 页面过渡效果 */
.page-transition {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.page-transition.entering {
  opacity: 0;
  transform: translateY(20rpx);
}

.page-transition.entered {
  opacity: 1;
  transform: translateY(0);
}

/* 底部安全区域 */
.safe-bottom {
  height: 120rpx;
  background: linear-gradient(to bottom, var(--bg-color) 0%, transparent 100%);
}

/* 跑团社区卡片样式 */
.running-team-card {
  padding: 24rpx;
  transition: all 0.3s ease;
}

.running-team-card:active {
  transform: scale(0.98);
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}

.team-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.team-logo {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 2rpx solid #f5f5f5;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.team-info {
  flex: 1;
}

.team-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx;
}

.team-location {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.team-location text {
  margin-left: 8rpx;
}

.team-description {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.team-tags {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16rpx;
}

.team-tag {
  padding: 6rpx 16rpx;
  font-size: 22rpx;
  color: #a5191e;
  background-color: rgba(165, 25, 30, 0.1);
  border-radius: 20rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.team-stats {
  display: flex;
  justify-content: space-between;
  border-top: 1rpx solid #f2f2f2;
  padding-top: 16rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.stat-item text {
  margin-left: 8rpx;
}
