const agreementConfig = require('../../../../config/agreement.js');

Page({
  data: {
    agreementContent: agreementConfig
  },

  onLoad: function(options) {
    wx.setNavigationBarTitle({
      title: '报名协议'
    });
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 同意协议并返回
  agreeAndBack: function() {
    // 可以在这里添加同意协议的逻辑
    wx.navigateBack({
      delta: 1
    });
  },

  // 联系客服
  contactService: function() {
    wx.showActionSheet({
      itemList: ['拨打客服电话', '发送邮件'],
      success: (res) => {
        if (res.tapIndex === 0) {
          wx.makePhoneCall({
            phoneNumber: this.data.agreementContent.contactInfo.phone
          });
        } else if (res.tapIndex === 1) {
          wx.setClipboardData({
            data: this.data.agreementContent.contactInfo.email,
            success: () => {
              wx.showToast({
                title: '邮箱已复制',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }
});
