/* 协议页面样式 */
.agreement-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding-bottom: 120rpx;
}

/* 头部样式 */
.agreement-header {
  background: linear-gradient(135deg, #a5191e 0%, #8b0000 100%);
  color: white;
  padding: 40rpx 32rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(165, 25, 30, 0.3);
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
}

.header-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

/* 协议内容样式 */
.agreement-content {
  padding: 32rpx;
}

.agreement-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #a5191e;
  margin-bottom: 20rpx;
  padding-bottom: 12rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.section-content {
  line-height: 1.8;
}

.content-item {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  padding-left: 20rpx;
  position: relative;
}

.content-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 14rpx;
  width: 8rpx;
  height: 8rpx;
  background: #a5191e;
  border-radius: 50%;
}

.content-item:last-child {
  margin-bottom: 0;
}

/* 联系信息样式 */
.contact-section {
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(165, 25, 30, 0.1);
}

.contact-content {
  margin-top: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
  padding: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.contact-icon {
  font-size: 32rpx;
  width: 32rpx;
  text-align: center;
}

.contact-item:active {
  background: rgba(165, 25, 30, 0.1);
  transform: scale(0.98);
}

.contact-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.work-time {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  margin-top: 16rpx;
  padding: 12rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8rpx;
}

/* 底部按钮样式 */
.agreement-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.footer-buttons {
  display: flex;
  gap: 16rpx;
}

.btn-secondary {
  flex: 1;
  height: 88rpx;
  line-height: 88rpx;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-secondary:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.98);
}

.btn-primary {
  flex: 2;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(135deg, #a5191e 0%, #8b0000 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(165, 25, 30, 0.3);
  transition: all 0.3s ease;
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(165, 25, 30, 0.4);
}

/* 响应式适配 */
@media (max-width: 375px) {
  .agreement-content {
    padding: 24rpx;
  }
  
  .agreement-section {
    padding: 24rpx;
  }
  
  .section-title {
    font-size: 28rpx;
  }
  
  .content-item {
    font-size: 26rpx;
  }
}
