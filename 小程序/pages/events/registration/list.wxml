<!--pages/events/registration/list.wxml-->
<view class="marathon-container">
  <!-- 顶部导航栏 - 现代化玻璃风格 -->
  <view class="top-header">
    <view class="header-left">
      <view class="back-icon" bindtap="goBack">
        <van-icon name="arrow-left" size="20px" color="#333" />
      </view>
      <text class="page-title">赛事报名</text>
    </view>
    <view class="header-actions">
      <view class="action-icon" bindtap="showFilter">
        <van-icon name="filter-o" size="20px" color="#333" />
      </view>
    </view>
  </view>

  <!-- 内容滚动区域 -->
  <scroll-view class="content-scroll" scroll-y="true" bindscrolltolower="loadMore" enhanced="true" show-scrollbar="false">
    
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-bar">
        <van-icon name="search" size="16px" color="#999" />
        <input class="search-input" 
               placeholder="搜索赛事名称、地点" 
               value="{{searchKeyword}}" 
               bindinput="onSearchInput" 
               bindconfirm="onSearchConfirm" />
        <van-icon wx:if="{{searchKeyword}}" name="clear" size="16px" color="#999" bindtap="clearSearch" />
      </view>
    </view>





    <!-- 赛事列表 -->
    <view class="events-container">
      <view class="events-list">
        <view class="event-card modern" 
              wx:for="{{filteredEvents}}" 
              wx:key="id" 
              data-id="{{item.id}}" 
              bindtap="onEventTap">
          
          <!-- 赛事图片容器 -->
          <view class="event-card-image-container">
            <image class="event-card-image" 
                   src="{{item.image || '/images/default-event.jpg'}}" 
                   mode="aspectFill" 
                   lazy-load="true" />
            <view class="event-card-overlay"></view>
            
            <!-- 状态标签 -->
            <view class="event-status-tag {{item.status}}">
              <text wx:if="{{item.status === 'signup'}}">报名中</text>
              <text wx:elif="{{item.status === 'upcoming'}}">即将开始</text>
              <text wx:elif="{{item.status === 'ongoing'}}">进行中</text>
              <text wx:elif="{{item.status === 'finished'}}">已结束</text>
              <text wx:elif="{{item.status === 'cancelled'}}">已取消</text>
              <text wx:else>{{item.status}}</text>
            </view>
            
            <!-- 热门标签 -->
            <view class="hot-tag" wx:if="{{item.isHot}}">
              <van-icon name="fire-o" size="12px" color="#fff" />
              <text>热门</text>
            </view>
            
            <!-- 倒计时标签 -->
            <view class="countdown-tag" wx:if="{{item.countdown && item.status === 'registration'}}">
              <text>{{item.countdown}}</text>
            </view>
          </view>

          <!-- 赛事内容 -->
          <view class="event-card-content">
            <!-- 标题 -->
            <view class="event-title-row">
              <text class="event-title">{{item.title}}</text>
              <!-- 价格暂时隐藏，因为一个赛事会有多个赛事类型，每个类型的价格不一致 -->
              <!-- <view class="event-price">
                <text class="price-symbol">¥</text>
                <text class="price-amount">{{item.price}}</text>
              </view> -->
            </view>

            <!-- 赛事信息 -->
            <view class="event-info-row">
              <view class="event-info-item">
                <van-icon name="location-o" size="12px" color="#666" />
                <text>{{item.location}}</text>
              </view>
              <view class="event-info-item">
                <van-icon name="clock-o" size="12px" color="#666" />
                <text>{{item.date}} {{item.time}}</text>
              </view>
            </view>

            <!-- 标签 -->
            <view class="event-tags">
              <text class="event-tag" wx:for="{{item.tags}}" wx:key="*this">{{item}}</text>
            </view>

            <!-- 底部信息 -->
            <view class="event-footer">
              <view class="event-action-btn {{item.status}}"
                    data-id="{{item.id}}"
                    bindtap="onRegisterTap">
                <text wx:if="{{item.status === 'registration'}}">立即报名</text>
                <text wx:elif="{{item.status === 'upcoming'}}">即将开始</text>
                <text wx:else>已结束</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view class="loading-more" wx:if="{{loading}}">
        <van-loading size="16px" color="#999" />
        <text>加载中...</text>
      </view>

      <!-- 没有更多数据 -->
      <view class="loading-more" wx:if="{{!hasMore && !loading && filteredEvents.length > 0}}">
        <text>没有更多数据了</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" wx:if="{{!loading && filteredEvents.length === 0}}">
        <van-icon name="search" size="48px" color="#ccc" />
        <text class="empty-title">暂无相关赛事</text>
        <text class="empty-desc">试试调整筛选条件或搜索关键词</text>
        <view class="empty-action" bindtap="resetFilters">
          <text>重置筛选</text>
        </view>
      </view>
    </view>

    <!-- 底部安全区域 -->
    <view class="safe-area-bottom"></view>
  </scroll-view>

  <!-- 筛选弹窗 -->
  <van-popup show="{{showFilterPopup}}" 
             position="bottom" 
             round="true" 
             bind:close="hideFilter">
    <view class="filter-popup">
      <view class="filter-popup-header">
        <text class="filter-popup-title">筛选条件</text>
        <van-icon name="cross" size="20px" color="#666" bindtap="hideFilter" />
      </view>

      <!-- 统计信息 -->
      <view class="stats-container">
        <view class="stats-card">
          <view class="stats-item">
            <text class="stats-number">{{stats.totalEvents}}</text>
            <text class="stats-label">总赛事</text>
          </view>
          <view class="stats-divider"></view>
          <view class="stats-item">
            <text class="stats-number">{{stats.registrationEvents}}</text>
            <text class="stats-label">报名中</text>
          </view>
        </view>
      </view>

      <!-- 赛事状态筛选 -->
      <view class="filter-section">
        <text class="filter-section-title">赛事状态</text>
        <view class="filter-options">
          <view class="filter-option {{item.active ? 'active' : ''}}" 
                wx:for="{{filterOptions.status}}" 
                wx:key="id" 
                data-type="status" 
                data-id="{{item.id}}" 
                bindtap="onFilterOptionTap">
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 距离筛选 -->
      <view class="filter-section">
        <text class="filter-section-title">赛事距离</text>
        <view class="filter-options">
          <view class="filter-option {{item.active ? 'active' : ''}}" 
                wx:for="{{filterOptions.distance}}" 
                wx:key="id" 
                data-type="distance" 
                data-id="{{item.id}}" 
                bindtap="onFilterOptionTap">
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 价格筛选 -->
      <view class="filter-section">
        <text class="filter-section-title">报名费用</text>
        <view class="filter-options">
          <view class="filter-option {{item.active ? 'active' : ''}}" 
                wx:for="{{filterOptions.price}}" 
                wx:key="id" 
                data-type="price" 
                data-id="{{item.id}}" 
                bindtap="onFilterOptionTap">
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 地区筛选 -->
      <view class="filter-section">
        <text class="filter-section-title">赛事地区</text>
        <view class="filter-options">
          <view class="filter-option {{item.active ? 'active' : ''}}" 
                wx:for="{{filterOptions.location}}" 
                wx:key="id" 
                data-type="location" 
                data-id="{{item.id}}" 
                bindtap="onFilterOptionTap">
            <text>{{item.name}}</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="filter-actions">
        <view class="filter-reset" bindtap="resetFilter">
          <text>重置</text>
        </view>
        <view class="filter-confirm" bindtap="confirmFilter">
          <text>确定</text>
        </view>
      </view>
    </view>
  </van-popup>
</view>
