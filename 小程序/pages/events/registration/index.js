const app = getApp();
const apiService = require('../../../utils/api.js');

// 协议内容配置
const agreementConfig = {
  title: '马拉松赛事报名协议',
  lastUpdate: '2024年7月9日',
  sections: [
    {
      title: '一、基本条款',
      content: [
        '1.1 参赛者须确保提供的个人信息真实、准确、完整，如因信息不实造成的一切后果由参赛者自行承担。',
        '1.2 参赛者须年满18周岁（以比赛当日为准），具备完全民事行为能力。',
        '1.3 参赛者须确保身体健康状况良好，适合参加马拉松长距离跑步运动。',
        '1.4 参赛者报名即表示同意本协议的全部条款。'
      ]
    },
    {
      title: '二、参赛规则',
      content: [
        '2.1 参赛者须严格遵守赛事规则，服从赛事工作人员、裁判员和安保人员的指挥。',
        '2.2 参赛者须按照指定路线参赛，不得抄近道或逆向跑步。',
        '2.3 参赛者须佩戴号码布参赛，号码布不得转让、涂改或遮挡。',
        '2.4 严禁替跑、蹭跑等违规行为，一经发现将取消参赛资格和成绩。'
      ]
    },
    {
      title: '三、健康与安全',
      content: [
        '3.1 参赛者须确保身体状况良好，建议赛前进行全面体检。',
        '3.2 患有心脏病、高血压、糖尿病、哮喘等疾病的人员不宜参加比赛。',
        '3.3 参赛者应根据自身情况合理分配体力，如感身体不适应立即停止比赛并寻求帮助。',
        '3.4 比赛过程中如发生意外，组委会将提供必要的医疗救助。'
      ]
    },
    {
      title: '四、费用与退款',
      content: [
        '4.1 报名费用一经支付，原则上不予退还。',
        '4.2 如因不可抗力因素（如恶劣天气、疫情、自然灾害等）导致赛事取消，组委会将根据实际情况处理退款事宜。',
        '4.3 参赛者因个人原因无法参赛的，报名费不予退还。'
      ]
    },
    {
      title: '五、其他条款',
      content: [
        '5.1 本协议的解释权归赛事组委会所有。',
        '5.2 如有争议，双方应友好协商解决；协商不成的，可向赛事举办地人民法院提起诉讼。',
        '5.3 本协议自参赛者确认报名时生效。'
      ]
    }
  ],
  contactInfo: {
    title: '联系我们',
    phone: '************',
    email: '<EMAIL>',
    workTime: '工作时间：周一至周五 9:00-18:00'
  }
};

Page({
  // 处理图片URL，拼接baseUrl
  getImageUrl(imageUrl) {
    if (!imageUrl) {
      return '/image/banners/default-banner.jpg'; // 保留默认图片逻辑
    }
    const baseUrl = app.globalData.baseUrl;

    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
      return imageUrl;
    }

    if (imageUrl.startsWith('/') && !imageUrl.startsWith('//')) {
      if (baseUrl) {
        const finalUrl = baseUrl + imageUrl;
        return finalUrl;
      } else {
        return imageUrl; 
      }
    }
    return imageUrl;
  },

  data: {
    statusBarHeight: 10, // 默认值，会在onLoad中获取
    currentStep: 1, // 当前报名步骤：1-选择项目，2-填写信息，3-确认支付，4-报名成功
    isLoading: true,
    eventInfo: {
      id: '',
      name: '',
      date: '',
      location: '',
      banner: '/image/banners/default-banner.jpg',
      status: '',
      countdownDays: 0,
      isHot: false,
      isFavorite: false,
      participants: 0,
      distance: '',
      rating: '',
      description: '',
      highlights: []
    },
    raceOptions: {},
    selectedRace: null,
    formData: {
      name: '李明',
      idCard: '320102199203158765',
      phone: '13912345678',
      gender: 'male',
      birthdate: '1992-03-15',
      emergencyContact: '王芳',
      emergencyPhone: '13987654321',
      bestRecord: '3小时45分钟',
      tShirtSize: 'L',
      size: 'L',
      emergency: {
        name: '王芳',
        phone: '13987654321',
        relation: '配偶'
      }
    },
    tShirtSizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
    tShirtSizeIndex: 3, // 默认选择L码（索引3）
    addOns: {},
    addOnPrices: {},
    healthDeclaration: {
      noDisease: false,
      fitForRace: false,
      followRules: false
    },
    agreementChecked: false,
    fees: {
      registration: 0,
      addOns: 0,
      total: 0
    },
    canSubmit: false,
    showPaymentPopup: false,
    paymentMethod: 'wechat',
    errors: {},
    selectedAddOns: [],
    availableAddOns: [],
    showPicker: false,
    pickerType: '',
    pickerData: [],
    animationData: {},
     // Vant 日期选择器相关
     showDatePicker: false,
     datePickerValue: new Date().getTime(),
     minDate: new Date('1950-01-01').getTime(),
     maxDate: new Date('2005-12-31').getTime(),

     // 协议相关
     showAgreementModal: false,
     agreementContent: agreementConfig
  },

  onLoad(options) {
    
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync();
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    });

    // 设置认证token
    const app = getApp();
    const openId = app.globalData.openId || wx.getStorageSync('open_id_cache');
    if (openId) {
      apiService.setToken(openId);
    }

    // 加载赛事信息
    this.loadEventInfo(options.id);
    this.updateFees();
    this.initAnimation();
    this.checkFormValidity(); // 检查表单有效性以更新按钮状态

    // 设置页面标题
    if (this.data.eventInfo.name) {
      wx.setNavigationBarTitle({
        title: this.data.eventInfo.name
      });
    }
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 图片加载成功
   */
  onImageLoad(e) {
  },

  /**
   * 图片加载失败
   */
  onImageError(e) {
  },

  /**
   * 分享赛事
   */
  shareEvent() {
    const { eventInfo } = this.data;
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 收藏/取消收藏赛事
   */
  favoriteEvent() {
    const { eventInfo } = this.data;
    const newFavoriteStatus = !eventInfo.isFavorite;
    
    this.setData({
      'eventInfo.isFavorite': newFavoriteStatus
    });
    
    wx.showToast({
      title: newFavoriteStatus ? '已收藏' : '已取消收藏',
      icon: 'success',
      duration: 1500
    });
  },



  /**
   * 图片预览
   */
  previewImage(e) {
    const url = e.currentTarget.dataset.url;
    const urls = e.currentTarget.dataset.urls;
    wx.previewImage({
      current: url,
      urls: urls
    });
  },



  // fetchUserBalance 函数已移除，因为余额支付已移除

  /**
   * 更新费用计算
   */
  updateFees() {
    const { selectedRace, raceOptions, addOns, availableAddOns } = this.data;
    const racePrice = raceOptions[selectedRace] ? raceOptions[selectedRace].price : 0;

    let addOnsTotal = 0;

    // 动态计算增值服务总价
    if (availableAddOns && availableAddOns.length > 0) {
      // 优先使用API返回的增值服务数据
      availableAddOns.forEach(service => {
        if (addOns[service.key]) {
          addOnsTotal += service.price || 0;
        }
      });
    } else {
      // 如果availableAddOns为空，则增值服务总价为0
      addOnsTotal = 0;
    }

    this.setData({
      'fees.registration': racePrice,
      'fees.addOns': addOnsTotal,
      'fees.total': racePrice + addOnsTotal
    }, () => {
      this.checkFormValidity();
    });
  },

  /**
   * 选择比赛项目
   */
  selectRace(e) {
    const race = e.currentTarget.dataset.race;

    this.setData({
      selectedRace: race
    }, () => {
      this.updateFees();
    });
  },

  // 从个人资料填充表单
  fillFromProfile: function() {
    wx.showLoading({
      title: '加载个人资料...',
    });

    // 虚拟个人资料数据
    setTimeout(() => {
      const userProfile = {
        name: '李明',
        idCard: '320102199203158765',
        phone: '13912345678',
        gender: 'male',
        birthdate: '1992-03-15',
        emergencyContact: '王芳',
        emergencyPhone: '13987654321',
        bestRecord: '3小时45分钟',
        tShirtSize: 'L'
      };

      // 更新T恤尺码索引
      const tShirtSizeIndex = this.data.tShirtSizes.indexOf(userProfile.tShirtSize);

      this.setData({
        formData: {
          ...this.data.formData,
          ...userProfile
        },
        tShirtSizeIndex: tShirtSizeIndex >= 0 ? tShirtSizeIndex : -1
      });

      wx.hideLoading();
      wx.showToast({
        title: '已填充个人资料',
        icon: 'success'
      });

      // 重新检查表单有效性
      this.checkFormValidity();
    }, 500);
  },

  // 选择性别
  selectGender: function(e) {
    const gender = e.currentTarget.dataset.gender;
    this.setData({
      'formData.gender': gender
    }, () => {
      this.checkFormValidity();
    });
  },

  // 选择出生日期 (保留原方法以兼容)
  bindDateChange: function(e) {
    this.setData({
      'formData.birthdate': e.detail.value
    }, () => {
      this.checkFormValidity();
    });
  },

  // 显示 Vant 日期选择器
  showDatePicker() {
    // 如果已有选择的日期，设置为当前值
    let currentDate = new Date().getTime();
    if (this.data.formData.birthdate) {
      currentDate = new Date(this.data.formData.birthdate).getTime();
    }
    
    this.setData({
      showDatePicker: true,
      datePickerValue: currentDate
    });
  },

  // 隐藏 Vant 日期选择器
  hideDatePicker() {
    this.setData({
      showDatePicker: false
    });
  },

  // 确认选择日期
  onDateConfirm(e) {
    const timestamp = e.detail;
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dateString = `${year}-${month}-${day}`;
    
    this.setData({
      'formData.birthdate': dateString,
      showDatePicker: false
    });
    this.checkFormValidity();
  },

  // 选择T恤尺码
  bindTShirtChange: function(e) {
    this.setData({
      tShirtSizeIndex: e.detail.value,
      'formData.tShirtSize': this.data.tShirtSizes[e.detail.value]
    }, () => {
      this.checkFormValidity();
    });
  },

  // 处理表单输入
  handleInput: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    const { formData } = this.data;
    
    const newFormData = { ...formData };
    newFormData[field] = value;
    
    this.setData({ formData: newFormData }, () => {
      this.checkFormValidity();
    });
  },

  // 切换增值服务
  toggleAddOn: function(e) {
    const { addon } = e.currentTarget.dataset;
    const { addOns } = this.data;
    
    // addOns是对象，不是数组
    const newAddOns = { ...addOns };
    newAddOns[addon] = !newAddOns[addon];
    
    this.setData({ addOns: newAddOns }, () => {
      this.updateFees();
    });
  },



  // 切换健康声明
  toggleDeclaration: function(e) {
    const { declaration } = e.currentTarget.dataset;
    const { healthDeclaration } = this.data;
    
    // healthDeclaration是对象，不是数组
    const newHealthDeclaration = { ...healthDeclaration };
    newHealthDeclaration[declaration] = !newHealthDeclaration[declaration];
    
    this.setData({ healthDeclaration: newHealthDeclaration }, () => {
      this.checkFormValidity();
    });
  },

  // 切换协议同意
  toggleAgreement: function(e) {
    this.setData({
      agreementChecked: !this.data.agreementChecked
    }, () => {
      this.checkFormValidity();
    });
  },

  // 验证表单并获取缺失字段
  validateFormAndGetMissingFields: function() {
    const { name, idCard, phone, gender, birthdate, emergencyContact, emergencyPhone } = this.data.formData;
    const { noDisease, fitForRace, followRules } = this.data.healthDeclaration;
    const agreementChecked = this.data.agreementChecked;
    const missingFields = [];

    // 检查个人信息
    if (!name || name.trim() === '') missingFields.push('• 真实姓名');
    if (!idCard || idCard.trim() === '') missingFields.push('• 身份证号码');
    if (!phone || phone.trim() === '') missingFields.push('• 手机号码');
    if (!gender) missingFields.push('• 性别');
    if (!birthdate) missingFields.push('• 出生日期');
    if (!emergencyContact || emergencyContact.trim() === '') missingFields.push('• 紧急联系人');
    if (!emergencyPhone || emergencyPhone.trim() === '') missingFields.push('• 紧急联系电话');

    // 检查健康声明
    if (!noDisease) missingFields.push('• 健康声明：确认无心脏病等疾病');
    if (!fitForRace) missingFields.push('• 健康声明：确认身体适合参加比赛');
    if (!followRules) missingFields.push('• 健康声明：同意遵守比赛规则');

    // 检查协议同意
    if (!agreementChecked) missingFields.push('• 同意报名协议');

    return missingFields;
  },

  // 检查表单有效性
  checkFormValidity: function() {
    const { name, idCard, phone, gender, birthdate, emergencyContact, emergencyPhone } = this.data.formData;
    const { noDisease, fitForRace, followRules } = this.data.healthDeclaration;
    const agreementChecked = this.data.agreementChecked;

    const isFormValid = name && idCard && phone && gender && birthdate && emergencyContact && emergencyPhone;
    const isHealthValid = noDisease && fitForRace && followRules;
    const canSubmit = isFormValid && isHealthValid && agreementChecked;

    // 调试信息：显示当前表单状态
    console.log('表单验证状态:', {
      姓名: !!name,
      身份证: !!idCard,
      手机: !!phone,
      性别: !!gender,
      生日: !!birthdate,
      紧急联系人: !!emergencyContact,
      紧急电话: !!emergencyPhone,
      健康声明: isHealthValid,
      协议同意: agreementChecked,
      可提交: canSubmit
    });

    this.setData({
      canSubmit: canSubmit
    });
  },

  // 显示协议详情
  showAgreement: function() {
    // 在当前页面显示协议内容
    this.setData({
      showAgreementModal: true
    });
  },

  // 关闭协议弹窗
  closeAgreementModal: function() {
    this.setData({
      showAgreementModal: false
    });
  },

  // 同意协议并关闭弹窗
  agreeAgreement: function() {
    this.setData({
      agreementChecked: true,
      showAgreementModal: false
    }, () => {
      this.checkCanSubmit();
    });
  },

  // 验证表单
  validateForm() {
    const { formData, selectedRace, healthDeclaration, agreementChecked } = this.data;
    
    if (!formData.name) {
      return { isValid: false, message: '请填写姓名' };
    }
    if (!formData.phone) {
      return { isValid: false, message: '请填写手机号' };
    }
    if (!/^1[3-9]\d{9}$/.test(formData.phone)) {
      return { isValid: false, message: '请填写正确的手机号' };
    }
    if (!formData.idCard) {
      return { isValid: false, message: '请填写身份证号' };
    }
    if (!/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(formData.idCard)) {
      return { isValid: false, message: '请填写正确的身份证号' };
    }
    if (!formData.emergencyContact) {
      return { isValid: false, message: '请填写紧急联系人' };
    }
    if (!formData.emergencyPhone) {
      return { isValid: false, message: '请填写紧急联系人电话' };
    }
    if (!/^1[3-9]\d{9}$/.test(formData.emergencyPhone)) {
      return { isValid: false, message: '请填写正确的紧急联系人电话' };
    }
    if (!selectedRace) {
      return { isValid: false, message: '请选择比赛项目' };
    }
    if (!healthDeclaration.noDisease || !healthDeclaration.fitForRace || !healthDeclaration.followRules) {
      return { isValid: false, message: '请确认健康声明' };
    }
    if (!agreementChecked) {
      return { isValid: false, message: '请同意报名协议' };
    }
    
    return { isValid: true };
  },

  // 构建订单数据
  buildOrderData() {
    const eventInfo = this.data.eventInfo;
    const formData = this.data.formData;
    const addOns = this.data.addOns;
    const fees = this.data.fees;
    
    // 获取openId
    const app = getApp();
    const openId = app.globalData.openId || wx.getStorageSync('open_id_cache');
    
    // 构建增值服务数组
    const selectedAddOns = [];
    const { availableAddOns } = this.data;
    
    if (addOns) {
      Object.keys(addOns).forEach(key => {
        if (addOns[key]) {
          // 优先从API返回的availableAddOns中获取价格和ID
          const addonService = availableAddOns.find(service => service.key === key);
          const price = addonService ? addonService.price : (this.data.addOnPrices[key] || 0);
          
          selectedAddOns.push({
            id: addonService ? addonService.id : null, // 包含服务ID
            name: key,
            price: price
          });
        }
      });
    }
    
    return {
      event_id: eventInfo.id,
      event_name: eventInfo.name,
      event_location:eventInfo.location,
      race_option_id: this.data.raceOptions[this.data.selectedRace].id,
      name: formData.name,
      phone: formData.phone,
      id_card: formData.idCard,
      gender: formData.gender,
      birthdate: formData.birthdate,
      emergency_contact: formData.emergencyContact,
      emergency_phone: formData.emergencyPhone,
      t_shirt_size: formData.tShirtSize,
      add_ons: JSON.stringify(selectedAddOns),
      total_fee: fees.total,
      openid: openId // 添加openid参数
    };
  },

  // 提交报名
  async submitRegistration() {
    // 验证表单
    const validation = this.validateForm();
    if (!validation.isValid) {
      wx.showToast({
        title: validation.message,
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 显示加载状态
    this.setData({ isSubmitting: true });

    try {
      // 确保认证token已设置
      const app = getApp();
      const openId = app.globalData.openId || wx.getStorageSync('open_id_cache');
      if (openId) {
        apiService.setToken(openId);
      } else {
        throw new Error('请先登录');
      }
      
      // 创建订单数据
      const orderData = this.buildOrderData();
      console.log('订单数据:', orderData);
      
      // 调用API创建订单
      const response = await apiService.createRegistrationOrder(orderData);
      console.log('API响应:', response);
      
      if (response && (response.code === 0 || response.code === 1) && response.data) {
        // 订单创建成功或已存在待支付订单，保存订单信息
        const orderInfo = response.data;
        this.setData({
          orderId: orderInfo.orderId,
          orderNo: orderInfo.orderNo,
          orderAmount: orderInfo.totalFee || this.data.fees.total
        });
        
        // 如果是已存在的订单，显示提示信息
        if (orderInfo.existing) {
          wx.showToast({
            title: '检测到未完成订单，请继续支付',
            icon: 'none',
            duration: 2000
          });
        }
        
        // 显示支付弹窗
        this.setData({
          showPaymentPopup: true,
          isSubmitting: false
        });
      } else {
        throw new Error(response?.msg || '创建订单失败');
      }
    } catch (error) {
      console.error('提交报名失败:', error);
      this.setData({ isSubmitting: false });
      wx.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'none'
      });
    }
  },

  // 关闭支付弹窗
  closePaymentPopup: function() {
    this.setData({
      showPaymentPopup: false
    });
  },

  // 选择支付方式
  selectPaymentMethod: function(e) {
    const method = e.currentTarget.dataset.method;

    // 余额支付相关检查已移除

    this.setData({
      paymentMethod: method
    });
  },

  // 确认支付
  async confirmPayment() {
    const { paymentMethod, orderId, orderAmount } = this.data;
    
    if (!paymentMethod) {
      wx.showToast({
        title: '请选择支付方式',
        icon: 'none'
      });
      return;
    }

    if (!orderId) {
      wx.showToast({
        title: '订单信息异常，请重新提交',
        icon: 'none'
      });
      return;
    }

    // 隐藏支付弹窗
    this.setData({ showPaymentPopup: false });

    // 显示支付加载
    wx.showLoading({
      title: '正在支付...',
      mask: true
    });

    try {
      // 获取openId
      const app = getApp();
      const openId = app.globalData.openId || wx.getStorageSync('open_id_cache');
      
      // 构建支付数据
      const paymentData = {
        order_no: this.data.orderNo,
        order_id: orderId,
        payment_method: paymentMethod,
        amount: orderAmount,
        openid: openId
      };

      // 调用支付API
      const response = await apiService.createPayment(paymentData);
      
      if (response && (response.code === 0 || response.code === 1) && response.data) {
        const paymentInfo = response.data;
        
        if (paymentMethod === 'wechat') {
          // 微信支付
          await this.processWechatPayment(paymentInfo);
        } else {
          // 其他支付方式
          await this.processOtherPayment(paymentInfo);
        }
      } else {
        throw new Error(response?.msg || '支付发起失败');
      }
    } catch (error) {
      console.error('支付失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: error.message || '支付失败，请重试',
        icon: 'none'
      });
    }
  },

  // 处理微信支付
  async processWechatPayment(paymentInfo) {
    try {
      // 调用微信支付
      await new Promise((resolve, reject) => { // <--- 移除 paymentResult 变量，直接 await Promise
        wx.requestPayment({
          timeStamp: paymentInfo.timeStamp,
          nonceStr: paymentInfo.nonceStr,
          package: paymentInfo.package,
          signType: 'MD5',
          paySign: paymentInfo.paySign,
          success: resolve, // <--- 仅 resolve
          fail: reject
        });
      });
      
      wx.hideLoading(); // 统一在这里隐藏加载
      wx.showToast({
        title: '支付成功',
        icon: 'success'
      });

      // 新增：主动通知后端确认支付状态
      apiService.confirmPaymentStatus(this.data.orderId)
        .then(res => {
          console.log('后端确认支付状态结果:', res);
        })
        .catch(err => {
          console.error('后端确认支付状态失败:', err);
        });
      
      // 跳转到订单详情页
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/events/registration/detail/index?orderId=${this.data.orderId}`
        });
      }, 1500);
    } catch (error) {
      wx.hideLoading();
      console.error('微信支付错误:', error);
      
      let errorMsg = '支付失败';
      if (error.errMsg && error.errMsg.includes('cancel')) {
        errorMsg = '支付已取消';
      } else if (error.errMsg) {
        if (error.errMsg.includes('param')) {
          errorMsg = '支付参数错误';
        } else if (error.errMsg.includes('sign')) {
          errorMsg = '支付签名验证失败';
        } else if (error.errMsg.includes('network')) {
          errorMsg = '网络连接失败';
        } else {
          errorMsg = `支付失败: ${error.errMsg}`;
        }
      }
      
      wx.showToast({
        title: errorMsg,
        icon: 'none',
        duration: 1500
      });
      
      // 无论支付成功、失败还是取消，都跳转到订单页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/events/registration/detail/index?orderId=${this.data.orderId}`
        });
      }, 1500);
    }
  },

  // 处理其他支付方式
  async processOtherPayment(paymentInfo) {
    // 其他支付方式的处理逻辑
    wx.hideLoading();
    wx.showToast({
      title: '支付成功',
      icon: 'success'
    });
    
    setTimeout(() => {
      wx.redirectTo({
        url: `/pages/events/registration/detail/index?orderId=${this.data.orderId}`
      });
    }, 1500);
  },

  // processBalancePay 函数已移除，因为余额支付已移除

  // 处理支付成功
  handlePaymentSuccess: function() {
    wx.showLoading({
      title: '提交报名信息...',
      mask: true
    });

    // 这里应该调用后端API提交报名信息
    // 示例代码，实际应替换为真实API调用
    setTimeout(() => {
      wx.hideLoading();

      // 关闭支付弹窗
      this.setData({
        showPaymentPopup: false,
        currentStep: 4 // 更新到第四步：报名成功
      });

      // 显示成功提示
      wx.showModal({
        title: '报名成功',
        content: '您已成功报名参加2024北京国际马拉松，请留意短信通知获取参赛物资领取信息。',
        showCancel: false,
        confirmText: '我知道了',
        success: (res) => {
          if (res.confirm) {
            // 跳转到报名成功页面或订单详情页面
            wx.redirectTo({
              url: '/pages/events/registration/success/index?orderId=123456'
            });
          }
        }
      });
    }, 1500);
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 初始化动画实例
  initAnimation() {
    this.animation = wx.createAnimation({
      duration: 300,
      timingFunction: 'ease'
    });
  },

  // 加载赛事信息
  async loadEventInfo(eventId) {
    
    if (!eventId) {
      this.setData({ isLoading: false });
      return;
    }

    this.setData({ isLoading: true });
    
    try {
      // 尝试调用真实API接口
      const app = getApp();
      const baseUrl = (app && app.globalData && app.globalData.baseUrl) || 'http://localhost:3000';
      
  
      
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: `${baseUrl}/api/events/detail?id=${eventId}`,
          method: 'GET',
          header: {
            'content-type': 'application/json'
          },
          success: resolve,
          fail: reject
        });
      });
      

      
      if (response.statusCode === 200 && response.data) {
        // 检查不同的API响应格式
        let eventData;
        if (response.data.success && response.data.data) {
          // 格式1: {success: true, data: {...}}
          eventData = response.data.data;
        } else if (response.data.code === 1 && response.data.data) {
          // 格式2: {code: 1, msg: "获取成功", data: {...}}
          eventData = response.data.data;
        } else if (response.data.id) {
          // 格式3: 直接返回数据对象
          eventData = response.data;
        } else {
          throw new Error('API返回数据格式不正确');
        }
        

        
        // 处理赛事数据并更新页面
        this.processEventData(eventData);
      } else {
        throw new Error('API返回错误: ' + (response.data?.message || response.data?.msg || '未知错误'));
      }
    } catch (error) {
      console.error('加载赛事信息失败:', error);
      wx.showToast({
        title: error.message || '加载赛事信息失败',
        icon: 'none'
      });
      this.setData({ isLoading: false });
    }
  },

  // 处理API返回的赛事数据
  processEventData(eventData) {
    let bannerPath = eventData.banner || eventData.image || eventData.poster || '';

    // 处理赛事亮点 - 支持字符串数组和对象数组两种格式
    let highlights = [];
    if (eventData.highlights && Array.isArray(eventData.highlights)) {
      if (typeof eventData.highlights[0] === 'string') {
        // 字符串数组格式，转换为对象数组
        highlights = eventData.highlights.map((item, index) => ({
          title: item,
          value: '✓'
        }));
      } else {
        // 对象数组格式，直接使用
        highlights = eventData.highlights;
      }
    } else {
      // 默认亮点
      highlights = [
        { title: '专业赛道', value: '国际标准赛道' },
        { title: '完赛奖牌', value: '精美纪念奖牌' },
        { title: '补给充足', value: '多个补给站点' },
        { title: '医疗保障', value: '全程医疗跟随' }
      ];
    }

    // 处理日期格式 - 支持多种日期格式
    let eventDate;
    let formattedDate = eventData.date || eventData.eventDate || eventData.eventTime;
    
    if (formattedDate) {
      eventDate = new Date(formattedDate);
      // 如果日期无效，尝试其他格式
      if (isNaN(eventDate.getTime())) {
        // 尝试解析 "YYYY-MM-DD HH:mm" 格式
        const dateStr = formattedDate.replace(/[年月]/g, '-').replace(/[日]/g, ' ');
        eventDate = new Date(dateStr);
      }
    } else {
      eventDate = new Date('2024-10-15');
    }

    // 计算倒计时
    const now = new Date();
    const timeDiff = eventDate.getTime() - now.getTime();
    const countdownDays = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));

    // 格式化显示日期
    const displayDate = eventData.date || eventData.eventDate || '2024年10月15日 08:00';
    
    // 处理赛事类型和价格
    let raceOptions = {};
    if (eventData.race_options && typeof eventData.race_options === 'object') {
      // API返回的是对象格式 - 新格式
      raceOptions = {};
      Object.keys(eventData.race_options).forEach(key => {
        const race = eventData.race_options[key];
        raceOptions[key] = {
          id: race.id,
          name: race.name,
          distance: race.distance, // distance in WXML will have '公里'
          price: race.price,
        };
      });
    } else if (eventData.raceTypes && Array.isArray(eventData.raceTypes)) {

    } else {
      // 如果API没有返回赛事类型，则raceOptions保持为空对象
      raceOptions = {};
    }

    // 处理增值服务 - 支持多种格式
    const addOnServicesData = eventData.addOnServices || eventData.addons || [];
    if (Array.isArray(addOnServicesData) && addOnServicesData.length > 0) {
      const availableAddOns = addOnServicesData.map(item => ({
        id: item.id, // 保留服务ID用于后端验证
        key: item.key || item.code || item.name.toLowerCase().replace(/\s+/g, '_'),
        name: item.name || item.title,
        price: item.price || item.fee,
        description: item.description || item.desc || ''
      }));
      this.setData({ availableAddOns });
    } else {
      this.setData({ availableAddOns: [] });
    }

    // 处理 banner URL
    const processedBannerUrl = this.getImageUrl(bannerPath);


    const processedEventInfo = {
      // 保留其他可能从API获取的原始字段
      ...eventData,
      // 覆盖特定字段
      id: eventData.id || eventData.event_id || 'default-event-id',
      name: (eventData.name || eventData.title || '精彩赛事').replace('FIXED: ', ''),
      date: displayDate, // 使用格式化后的日期
      location: eventData.location || eventData.address || '赛事地点',
      status: eventData.status || 'registering',
      countdownDays: countdownDays,
      isHot: eventData.is_hot || eventData.isHot || false,
      isFavorite: eventData.is_favorite || eventData.isFavorite || false,
      participants: eventData.participants_count || eventData.participantsCount || eventData.participants || eventData.registered_count || eventData.registeredCount || 0,
      distance: eventData.distance || '未知',
      rating: eventData.rating || 'N/A',
      description: eventData.description || eventData.details || '暂无详细介绍。',
      highlights: highlights,
      gallery: eventData.gallery || [],
      organizer: eventData.organizer || '未知主办方',
      price: selectedRaceKey ? raceOptions[selectedRaceKey].price : 0, // 默认显示最低价格或0
      banner: processedBannerUrl // 使用处理后的完整URL，确保不被覆盖
    };



    let selectedRaceKey = null;
    let minPrice = Infinity;

    const raceKeys = Object.keys(raceOptions);
    if (raceKeys.length > 0) {
      for (const key of raceKeys) {
        if (raceOptions[key].price < minPrice) {
          minPrice = raceOptions[key].price;
          selectedRaceKey = key;
        }
      }
    }
    
    this.setData({
      eventInfo: processedEventInfo,
      raceOptions: raceOptions,
      selectedRace: selectedRaceKey, // Set default selected race (null if no races)
      isLoading: false
    }, () => {
      this.updateFees(); // 更新费用信息
      this.checkFormValidity(); // 检查表单有效性
    });
  },

  // 表单输入处理
  onInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: ''
    }, () => {
      this.checkFormValidity();
    });
  },

  // 显示选择器
  showPicker(e) {
    const { type, data } = e.currentTarget.dataset;
    this.setData({
      showPicker: true,
      pickerType: type,
      pickerData: data
    });
  },

  // 选择器确认
  onPickerConfirm(e) {
    const { value } = e.detail;
    const { pickerType } = this.data;
    
    this.setData({
      [`formData.${pickerType}`]: value,
      showPicker: false
    });
  },

  // 选择器取消
  onPickerCancel() {
    this.setData({
      showPicker: false
    });
  },

  // 选择尺码
  selectSize(e) {
    const { size } = e.currentTarget.dataset;
    this.setData({
      'formData.size': size
    });
  },

  // 显示错误提示
  showError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  }
});
