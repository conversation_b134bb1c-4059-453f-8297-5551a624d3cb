/* pages/events/registration/list.wxss */

/* 继承首页的CSS变量和基础样式 */
page {
  /* 主色调 - 马拉松红色系 */
  --primary-color: #a5191e;
  --primary-light: #f8e8e8;
  --primary-dark: #800000;
  --primary-gradient: linear-gradient(135deg, #a5191e, #800000);
  
  /* 辅助色 - 活力橙色系 */
  --accent-color: #ff8f1f;
  --accent-light: #ffebcc;
  --accent-gradient: linear-gradient(135deg, #ff8f1f, #ff6b00);
  
  /* 功能色 */
  --success-color: #25b864;
  --success-light: #e6f7ee;
  --info-color: #2d8cf0;
  --info-light: #e8f4ff;
  --warning-color: #ffb020;
  --warning-light: #fff8e6;
  
  /* 文本色 */
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --text-white: #ffffff;
  
  /* 背景与边框 */
  --border-color: #eeeeee;
  --bg-color: #f7f8fa;
  --card-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --primary-shadow: 0 4rpx 16rpx rgba(165, 25, 30, 0.2);
  --accent-shadow: 0 4rpx 16rpx rgba(255, 143, 31, 0.2);

  /* 动画时间 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* 响应式设计变量 */
  --page-padding: 24rpx;
  --card-border-radius: 16rpx;
  --header-height: 88rpx;
  --safe-area-bottom: env(safe-area-inset-bottom, 0);

  font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-color);
  background-color: #f7f8fa;
  box-sizing: border-box;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 40%;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.05) 0%, rgba(212, 40, 46, 0.02) 100%);
  z-index: -1;
}

.marathon-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: env(safe-area-inset-bottom);
}

/* 顶部导航栏 - 高端玻璃风格 */
.top-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: 16rpx 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4rpx 30rpx rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  border-bottom-left-radius: 30rpx;
  border-bottom-right-radius: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.18);
  animation: slideDown 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.header-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.back-icon {
  margin-right: 16rpx;
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.back-icon:active {
  background-color: rgba(165, 25, 30, 0.1);
  transform: scale(0.9);
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  margin-left: 8rpx;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.action-icon {
  padding: 8rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.action-icon:active {
  background-color: rgba(165, 25, 30, 0.1);
  transform: scale(0.9);
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  padding-top: 120rpx;
  box-sizing: border-box;
}

/* 搜索栏 */
.search-container {
  padding: 0 24rpx 20rpx;
  animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-bar {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  padding: 16rpx 28rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06), inset 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  gap: 16rpx;
}

.search-bar:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3rpx var(--primary-light), 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  color: var(--text-color);
}

/* 筛选标签 */
.filter-tabs {
  padding: 0 24rpx 20rpx;
  animation: fadeInUp 0.7s ease;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-tab {
  display: inline-flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 30rpx;
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(5px);
  position: relative;
  overflow: hidden;
}

.filter-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.filter-tab:active::before {
  left: 100%;
}

.filter-tab.active {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--primary-shadow);
  transform: translateY(-2rpx);
}

.filter-tab.active::before {
  display: none;
}

/* 统计信息卡片 */
.stats-container {
  padding: 0 24rpx 24rpx;
  animation: fadeInUp 0.8s ease;
}

.stats-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.85) 100%);
  border-radius: 20rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(from 0deg, transparent, rgba(165, 25, 30, 0.1), transparent);
  animation: rotate 8s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  text-shadow: 0 2rpx 4rpx rgba(165, 25, 30, 0.2);
}

.stats-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.stats-divider {
  width: 1rpx;
  height: 40rpx;
  background: linear-gradient(to bottom, transparent, var(--border-color), transparent);
}

/* 赛事列表容器 */
.events-container {
  padding: 0 24rpx;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 赛事卡片 - 现代化设计 */
.event-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  animation: fadeInUp 0.6s ease;
  animation-fill-mode: both;
}

.event-card:nth-child(1) { animation-delay: 0.1s; }
.event-card:nth-child(2) { animation-delay: 0.2s; }
.event-card:nth-child(3) { animation-delay: 0.3s; }
.event-card:nth-child(4) { animation-delay: 0.4s; }
.event-card:nth-child(5) { animation-delay: 0.5s; }

.event-card:active {
  transform: translateY(4rpx) scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
}

.event-card.modern:hover {
  transform: translateY(-8rpx);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 赛事图片容器 */
.event-card-image-container {
  position: relative;
  height: 200rpx;
  overflow: hidden;
}

.event-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.event-card:active .event-card-image {
  transform: scale(1.05);
}

.event-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.4) 100%);
  z-index: 1;
}

/* 状态标签 */
.event-status-tag {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 12px;
  font-weight: 600;
  z-index: 2;
  backdrop-filter: blur(10px);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.event-status-tag.registration {
  background: rgba(37, 184, 100, 0.9);
  color: white;
}

.event-status-tag.upcoming {
  background: rgba(45, 140, 240, 0.9);
  color: white;
}

.event-status-tag.ended {
  background: rgba(153, 153, 153, 0.9);
  color: white;
}

.hot-tag {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  display: flex;
  align-items: center;
  gap: 4rpx;
  padding: 6rpx 12rpx;
  background: rgba(255, 143, 31, 0.9);
  color: white;
  border-radius: 16rpx;
  font-size: 11px;
  font-weight: 600;
  z-index: 2;
  backdrop-filter: blur(10px);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.countdown-tag {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  padding: 6rpx 12rpx;
  background: rgba(165, 25, 30, 0.9);
  color: white;
  border-radius: 16rpx;
  font-size: 11px;
  font-weight: 600;
  z-index: 2;
  backdrop-filter: blur(10px);
}

/* 赛事内容 */
.event-card-content {
  padding: 24rpx;
}

.event-title-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.event-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.3;
  flex: 1;
  margin-right: 16rpx;
}

.event-price {
  display: flex;
  align-items: baseline;
  color: var(--primary-color);
  font-weight: 700;
}

.price-symbol {
  font-size: 14px;
  margin-right: 2rpx;
}

.price-amount {
  font-size: 20px;
}

.event-info-row {
  display: flex;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.event-info-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 13px;
  color: var(--text-secondary);
}

.event-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.event-tag {
  padding: 4rpx 12rpx;
  background: var(--primary-light);
  color: var(--primary-color);
  border-radius: 12rpx;
  font-size: 11px;
  font-weight: 500;
}

.event-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.event-action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.event-action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.event-action-btn:active::before {
  width: 200%;
  height: 200%;
}

.event-action-btn.registration {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--primary-shadow);
}

.event-action-btn.upcoming {
  background: var(--info-color);
  color: white;
}

.event-action-btn.ended {
  background: var(--text-light);
  color: white;
}

/* 加载更多 */
.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  padding: 40rpx 0;
  font-size: 14px;
  color: var(--text-secondary);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-secondary);
  margin: 24rpx 0 12rpx;
}

.empty-desc {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.empty-action {
  padding: 16rpx 32rpx;
  background: var(--primary-gradient);
  color: white;
  border-radius: 24rpx;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.empty-action:active {
  transform: scale(0.95);
}

/* 筛选弹窗 */
.filter-popup {
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 24rpx;
  max-height: 80vh;
  overflow-y: auto;
}

.filter-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid var(--border-color);
}

.filter-popup-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-color);
}

.filter-section {
  margin-bottom: 32rpx;
}

.filter-section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 16rpx;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-option {
  padding: 12rpx 24rpx;
  background: var(--bg-color);
  border: 1rpx solid var(--border-color);
  border-radius: 24rpx;
  font-size: 14px;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.filter-option.active {
  background: var(--primary-gradient);
  color: white;
  border-color: var(--primary-color);
}

.filter-actions {
  display: flex;
  gap: 16rpx;
  margin-top: 32rpx;
}

.filter-reset,
.filter-confirm {
  flex: 1;
  padding: 16rpx;
  border-radius: 24rpx;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
}

.filter-reset {
  background: var(--bg-color);
  color: var(--text-secondary);
  border: 1rpx solid var(--border-color);
}

.filter-confirm {
  background: var(--primary-gradient);
  color: white;
}

.filter-reset:active,
.filter-confirm:active {
  transform: scale(0.95);
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom, 32rpx);
  background: transparent;
}

/* 响应式设计 */
@media screen and (min-width: 768px) {
  .events-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }
  
  .event-card {
    margin-bottom: 0;
  }
}

/* 深色模式适配 */
/* 深色模式已移除，保持统一的浅色主题 */
