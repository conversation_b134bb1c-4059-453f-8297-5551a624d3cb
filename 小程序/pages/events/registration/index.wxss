/* 全局变量 */
page {
  --primary-color: #a5191e;
  --primary-light: #f8e8e8;
  --primary-dark: #800000;
  --primary-gradient: linear-gradient(135deg, #a5191e, #800000);
  
  --accent-color: #ff8f1f;
  --accent-light: #ffebcc;
  --accent-gradient: linear-gradient(135deg, #ff8f1f, #ff6b00);
  
  --success-color: #25b864;
  --success-light: #e6f7ee;
  --info-color: #2d8cf0;
  --info-light: #e8f4ff;
  --warning-color: #ffb020;
  --warning-light: #fff8e6;
  
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  
  --border-color: #eeeeee;
  --bg-color: #f7f8fa;
  
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  --safe-area-bottom: env(safe-area-inset-bottom);
  --page-spacing: 24rpx;
  --border-radius: 16rpx;
  --border-radius-lg: 20rpx;
  --card-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 页面容器 */
.marathon-container {
  min-height: 100vh;
  background-color: var(--bg-color);
  padding-bottom: calc(120rpx + var(--safe-area-bottom));
}

/* 内容滚动区域 */
.content-scroll {
  height: 100vh;
  position: relative;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 英雄区域 */
.event-hero-section {
  position: relative;
  height: 750rpx;
  overflow: hidden;
}

.hero-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1.1);
  animation: subtle-zoom 20s infinite alternate ease-in-out;
}

.hero-overlay-gradient {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.2) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    rgba(0, 0, 0, 0.8) 100%
  );
  opacity: 0;
  animation: fadeIn 1s ease-out forwards;
}

/* 粒子动画背景 */
.hero-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 6s infinite ease-in-out;
}

.particle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.particle-2 {
  top: 60%;
  left: 80%;
  animation-delay: 2s;
}

.particle-3 {
  top: 80%;
  left: 20%;
  animation-delay: 4s;
}

.particle-4 {
  top: 30%;
  left: 70%;
  animation-delay: 1s;
}

.particle-5 {
  top: 50%;
  left: 50%;
  animation-delay: 3s;
}

.hero-content {
  position: absolute;
  bottom: 48rpx;
  left: var(--page-spacing);
  right: var(--page-spacing);
  color: #fff;
  z-index: 2;
  animation: slideUp 1s ease-out;
}

.fade-in-up {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 24rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.gradient-text {
  background: linear-gradient(135deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 24rpx;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border-radius: 30rpx;
  font-size: 26rpx;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out 0.3s backwards;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out 0.4s backwards;
}

.meta-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 状态标签 */
.status-badge {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  backdrop-filter: blur(10px);
  animation: slideInLeft 0.8s ease-out;
  z-index: 3;
}

.animated-badge {
  animation: badgePulse 2s infinite ease-in-out;
}

.pulse-animation {
  animation: pulse 1.5s infinite ease-in-out;
}

.status-icon {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: currentColor;
}

.status-badge.registering {
  background: rgba(37, 184, 100, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-badge.upcoming {
  background: rgba(45, 140, 240, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 热门标签 */
.hot-badge {
  position: absolute;
  top: 32rpx;
  right: 200rpx;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(255, 143, 31, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  animation: slideInLeft 0.8s ease-out 0.2s backwards;
  z-index: 3;
}

.flame-animation {
  animation: flame 1s infinite alternate ease-in-out;
}

.flame-effect {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 8rpx;
  height: 8rpx;
  background: #ff4444;
  border-radius: 50%;
  animation: flicker 0.5s infinite alternate;
}

/* 倒计时标签 */
.countdown-badge {
  position: absolute;
  top: 32rpx;
  right: 116rpx;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: rgba(45, 140, 240, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  animation: slideInLeft 0.8s ease-out 0.1s backwards;
  z-index: 3;
}

.bounce-animation {
  animation: bounce 2s infinite ease-in-out;
}

.countdown-ring {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 30rpx;
  animation: ring-pulse 2s infinite ease-in-out;
}

/* 价格信息 */
.price-container {
  margin-top: 24rpx;
  display: flex;
  align-items: baseline;
  gap: 12rpx;
  position: relative;
}

.shimmer-effect {
  animation: shimmer 2s infinite ease-in-out;
}

.price-glow {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  border-radius: 8rpx;
  animation: glow-rotate 3s infinite linear;
  pointer-events: none;
}

.price-label {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

.price-value {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price-symbol {
  font-size: 32rpx;
  color: #fff;
}

.price-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #fff;
}

.price-unit {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
}

/* 快速信息卡片 */
.quick-info-section {
  margin: -60rpx var(--page-spacing) 32rpx;
  position: relative;
  z-index: 10;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.info-card {
  background: #fff;
  padding: 32rpx 24rpx;
  border-radius: var(--border-radius);
  text-align: center;
  box-shadow: var(--card-shadow);
  animation: fadeInUp 0.8s ease-out backwards;
  transition: all var(--transition-fast);
}

.card-3d {
  transform-style: preserve-3d;
  transition: transform 0.3s ease;
}

.card-3d:hover {
  transform: rotateX(5deg) rotateY(5deg) translateZ(10rpx);
}

.glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.hover-float {
  transition: transform 0.3s ease;
}

.hover-float:hover {
  transform: translateY(-8rpx);
}

.info-card:nth-child(1) { animation-delay: 0.1s; }
.info-card:nth-child(2) { animation-delay: 0.2s; }
.info-card:nth-child(3) { animation-delay: 0.3s; }

.card-icon {
  margin-bottom: 16rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-number {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.card-label {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 内容区块 */
.content-section {
  margin: 32rpx var(--page-spacing);
  background: #fff;
  border-radius: var(--border-radius-lg);
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  animation: fadeIn 0.8s ease-out;
  transition: transform var(--transition-normal);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.content-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

/* 统一容器样式 */
.section-container {
  margin: 32rpx var(--page-spacing);
  background: #fff;
  border-radius: var(--border-radius-lg);
  padding: 32rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.section-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.interactive-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.15);
}

.modern-form {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.content-section:active {
  transform: scale(0.98);
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.section-icon {
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border-radius: 50%;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
}

.section-content {
  margin-top: 24rpx;
  line-height: 1.6;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10rpx);
  border-radius: var(--border-radius);
  padding: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.description-text {
  font-size: 28rpx;
  color: var(--text-color);
  line-height: 1.8;
}

.typewriter-effect {
  animation: typewriter 2s ease-out;
}

.title-underline {
  width: 40rpx;
  height: 3rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  border-radius: 2rpx;
  margin-left: auto;
}

.modern-header {
  position: relative;
  padding-bottom: 16rpx;
}

.icon-glow {
  animation: iconGlow 2s infinite ease-in-out;
}

/* 赛事描述 */
.description-text {
  font-size: 28rpx;
  line-height: 1.8;
  color: var(--text-secondary);
  text-align: justify;
}

/* 赛事亮点 */
.highlights-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
  margin-top: 24rpx;
}

.highlight-card {
  background: var(--bg-color);
  padding: 24rpx;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
}

.highlight-card:active {
  transform: translateY(2rpx);
}

.highlight-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border-radius: 12rpx;
  font-size: 24rpx;
}

.highlight-content {
  flex: 1;
}

.highlight-title {
  font-size: 28rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 4rpx;
}

.highlight-value {
  font-size: 24rpx;
  color: var(--text-secondary);
}

/* 赛事图库样式已移除 */

/* 报名步骤 */
.steps-container {
  margin: 32rpx var(--page-spacing);
  padding: 32rpx;
  background: #fff;
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.steps-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
  z-index: 0;
}

.step {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  position: relative;
  z-index: 1;
  transition: all var(--transition-normal);
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: var(--text-light);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.step.active .step-number {
  background: var(--primary-color);
  transform: scale(1.1);
  box-shadow: 0 4rpx 12rpx rgba(165, 25, 30, 0.3);
}

.step-text {
  font-size: 26rpx;
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.step.active .step-text {
  color: var(--primary-color);
  font-weight: bold;
}

.step-line {
  position: absolute;
  top: 56rpx;
  left: 15%;
  right: 15%;
  height: 2rpx;
  background: var(--border-color);
  z-index: 0;
  transition: background-color var(--transition-normal);
}

.step-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: var(--primary-color);
  transition: width 0.3s ease;
}

.step.active ~ .step-line::after {
  width: 100%;
}

/* 赛事选项 */
.race-options {
  margin-top: 24rpx;
}

.race-option {
  background: #fff;
  margin-bottom: 24rpx;
  padding: 32rpx;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  position: relative;
  overflow: hidden;
}

.option-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(45, 140, 240, 0.1), transparent);
  transition: left 0.5s ease;
}

.race-option:hover .option-glow {
  left: 100%;
}

.option-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  padding: 8rpx 16rpx;
  background: linear-gradient(135deg, #2d8cf0, #19be6b);
  color: white;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
}

.race-option.selected {
  background: var(--primary-light);
  border-color: var(--primary-color);
}

.race-option:active {
  transform: scale(0.98);
}

.race-option-content {
  flex: 1;
}

.race-name {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.race-details {
  display: flex;
  gap: 24rpx;
  font-size: 26rpx;
  color: var(--text-secondary);
}

.race-distance {
  color: var(--text-secondary);
}

.race-price {
  color: var(--primary-color);
  font-weight: bold;
}

.race-option-check {
  margin-left: 24rpx;
  transition: all 0.3s ease;
}

/* 动画 */
@keyframes subtle-zoom {
  from { transform: scale(1.1); }
  to { transform: scale(1.15); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(30rpx);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式调整 */
@media screen and (min-width: 768px) {
  .hero-image-container {
    height: 600rpx;
  }
  
  .hero-title {
    font-size: 48rpx;
  }
  
  .highlights-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  /* 图库网格样式已移除 */
  
  .race-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx;
  }
  
  .race-option {
    margin-bottom: 0;
  }
}

/* 表单容器 */
.form-section {
  margin: var(--page-spacing);
  padding: 32rpx;
  background: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
}

.form-group {
  margin-bottom: 32rpx;
}

/* 现代化表单样式 */
.modern-form {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.modern-input {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  padding: 20rpx 24rpx;
  transition: all 0.3s ease;
  overflow: hidden;
}

.modern-input::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.01) 0%, transparent 50%);
  pointer-events: none;
}

.modern-input:focus-within {
  border-color: var(--primary-color);
  box-shadow: 0 4rpx 16rpx rgba(165, 25, 30, 0.15);
}

.modern-input .form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-color);
  position: relative;
  z-index: 1;
}

.modern-input .form-label.required::after {
  content: '*';
  color: var(--primary-color);
  margin-left: 4rpx;
  font-weight: bold;
}

.modern-input .form-input {
  width: 100%;
  height: 44rpx;
  padding: 0;
  border: none;
  background: transparent;
  font-size: 28rpx;
  color: var(--text-color);
  position: relative;
  z-index: 1;
}

.modern-input .form-input::placeholder {
  color: var(--text-light);
  font-size: 26rpx;
}

.input-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2rpx;
  background: var(--primary-gradient);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.modern-input:focus-within .input-underline {
  width: 100%;
}

/* 现代化单选框样式 */
.modern-radio {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  padding: 20rpx 24rpx;
  overflow: hidden;
}

.modern-radio::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.01) 0%, transparent 50%);
  pointer-events: none;
}

.modern-radio .form-label {
  display: block;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-color);
  position: relative;
  z-index: 1;
}

.modern-radio .form-label.required::after {
  content: '*';
  color: var(--primary-color);
  margin-left: 4rpx;
  font-weight: bold;
}

.form-radio-group {
  display: flex;
  gap: 32rpx;
  position: relative;
  z-index: 1;
}

.modern-radio-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  border-radius: var(--border-radius);
  background: rgba(255, 255, 255, 0.5);
  border: 1rpx solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  min-width: 120rpx;
  justify-content: center;
}

.modern-radio-item:active {
  transform: scale(0.95);
}

.modern-radio-item.selected {
  background: rgba(165, 25, 30, 0.1);
  border-color: var(--primary-color);
}

.modern-radio-item text {
  font-size: 26rpx;
  color: var(--text-color);
  font-weight: 500;
}

.radio-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(165, 25, 30, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.modern-radio-item:active .radio-ripple {
  width: 80rpx;
  height: 80rpx;
}

/* 现代化选择器样式 */
.modern-picker {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  padding: 20rpx 24rpx;
  overflow: hidden;
}

.modern-picker::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.01) 0%, transparent 50%);
  pointer-events: none;
}

.modern-picker .form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  color: var(--text-color);
  position: relative;
  z-index: 1;
}

.modern-picker .form-label.required::after {
  content: '*';
  color: var(--primary-color);
  margin-left: 4rpx;
  font-weight: bold;
}

.form-picker {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 44rpx;
  position: relative;
  z-index: 1;
}

.form-picker text {
  font-size: 28rpx;
  color: var(--text-color);
}

.picker-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2rpx;
  background: var(--primary-gradient);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.modern-picker:active .picker-underline {
  width: 100%;
}

.form-floating {
  position: relative;
  margin-bottom: 40rpx;
}

.form-floating .form-input {
  padding-top: 28rpx;
  padding-bottom: 12rpx;
}

.form-floating .form-label {
  position: absolute;
  top: 20rpx;
  left: 24rpx;
  color: #999;
  transition: all 0.3s ease;
  pointer-events: none;
}

.form-floating .form-input:focus + .form-label,
.form-floating .form-input:not(:placeholder-shown) + .form-label {
  top: 8rpx;
  font-size: 22rpx;
  color: var(--primary-color);
}

.input-glow {
  position: relative;
  overflow: hidden;
}

.input-glow::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.input-glow:focus-within::after {
  width: 100%;
}

.form-group:last-child {
  margin-bottom: 0;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  margin-bottom: 16rpx;
  color: var(--text-color);
  font-size: 28rpx;
  font-weight: 500;
}

.required-mark {
  color: var(--primary-color);
  font-weight: bold;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-radius: calc(var(--border-radius) / 2);
  font-size: 28rpx;
  color: var(--text-color);
}

.form-input-error {
  border-color: var(--primary-color);
  background: var(--primary-light);
}

.error-message {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: var(--primary-color);
  animation: fadeIn 0.3s ease-out;
}

/* 选择器样式 */
.picker-group {
  position: relative;
}

.picker-value {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border: 2rpx solid var(--border-color);
  border-radius: calc(var(--border-radius) / 2);
  font-size: 28rpx;
  color: var(--text-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.picker-placeholder {
  color: var(--text-light);
}

.picker-arrow {
  color: var(--text-light);
  transition: transform var(--transition-fast);
}

.picker-group.active .picker-arrow {
  transform: rotate(180deg);
}

/* 复选框组样式 */
.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.checkbox-item {
  flex: 1 1 calc(50% - 8rpx);
  min-width: 300rpx;
  height: 88rpx;
  position: relative;
}

.checkbox-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.checkbox-label {
  width: 100%;
  height: 100%;
  padding: 0 24rpx;
  background: #fff;
  border: 2rpx solid var(--border-color);
  border-radius: calc(var(--border-radius) / 2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: var(--text-color);
  transition: all var(--transition-fast);
}

.checkbox-input:checked + .checkbox-label {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: #fff;
}

/* 提交按钮区域 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx var(--page-spacing) calc(24rpx + var(--safe-area-bottom));
  background: #fff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.submit-button {
  width: 100%;
  height: 88rpx;
  background: var(--primary-gradient);
  border-radius: calc(var(--border-radius) / 2);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.submit-button:active {
  transform: translateY(2rpx);
  opacity: 0.9;
}

.submit-button.disabled {
  background: var(--border-color);
  color: var(--text-light);
  pointer-events: none;
}

/* 协议确认区域 */
.agreement-section {
  margin: 32rpx var(--page-spacing);
  padding: 24rpx;
  background: var(--primary-light);
  border-radius: var(--border-radius);
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
}

.agreement-text {
  flex: 1;
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}

.agreement-link {
  color: var(--primary-color);
  text-decoration: underline;
}

/* 底部固定操作栏 */

/* 提交按钮 - 高级版 */
.submit-btn.premium-submit-btn {
  min-width: 300rpx;
  width: auto;
  max-width: 400rpx;
  height: 88rpx;
  background: linear-gradient(135deg, #a5191e 0%, #d32f2f 50%, #a5191e 100%);
  background-size: 200% 200%;
  color: #fff;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(165, 25, 30, 0.4), 0 4rpx 8rpx rgba(165, 25, 30, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  letter-spacing: 1rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  padding: 0 24rpx;
  white-space: nowrap;
}

.submit-btn.premium-submit-btn.active {
  animation: gradientShift 3s ease-in-out infinite, buttonPulse 2s ease-in-out infinite;
}

.submit-btn.premium-submit-btn.active:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 32rpx rgba(165, 25, 30, 0.5), 0 6rpx 12rpx rgba(165, 25, 30, 0.3);
}

.submit-btn.premium-submit-btn.disabled {
  background: linear-gradient(135deg, #cccccc, #999999);
  color: #666666;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: none;
  animation: none;
}

/* 按钮背景效果 */
.btn-background-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.submit-btn.premium-submit-btn.active .btn-background-effect {
  animation: shimmerSlide 2s ease-in-out infinite;
}

.btn-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  position: relative;
  z-index: 3;
  width: 100%;
  flex-wrap: nowrap;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: iconRotate 2s linear infinite;
}

.btn-text {
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  flex-shrink: 0;
}

.btn-price {
  font-size: 24rpx;
  font-weight: 600;
  opacity: 0.95;
  background: rgba(255, 255, 255, 0.3);
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.5);
  line-height: 1.2;
  backdrop-filter: blur(4px);
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 60rpx;
  text-align: center;
}

/* 按钮光泽效果 */
.btn-shine {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
}

.submit-btn.premium-submit-btn.active .btn-shine {
  opacity: 1;
  animation: shineWave 3s ease-in-out infinite;
}

/* 粒子效果 */
.btn-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.btn-particles .particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  opacity: 0;
}

.btn-particles .particle:nth-child(1) {
  top: 20%;
  left: 20%;
  animation: particleFloat1 4s ease-in-out infinite;
}

.btn-particles .particle:nth-child(2) {
  top: 60%;
  left: 70%;
  animation: particleFloat2 3s ease-in-out infinite 1s;
}

.btn-particles .particle:nth-child(3) {
  top: 80%;
  left: 30%;
  animation: particleFloat3 5s ease-in-out infinite 2s;
}

/* 按钮动画关键帧 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

@keyframes buttonPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes shimmerSlide {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes iconRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shineWave {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes particleFloat1 {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translateY(-20rpx) scale(1);
  }
}

@keyframes particleFloat2 {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0.3);
  }
  50% {
    opacity: 0.8;
    transform: translateY(-15rpx) scale(0.8);
  }
}

@keyframes particleFloat3 {
  0%, 100% {
    opacity: 0;
    transform: translateY(0) scale(0.4);
  }
  50% {
    opacity: 0.6;
    transform: translateY(-25rpx) scale(1.2);
  }
}
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  padding: 24rpx var(--page-spacing);
  padding-bottom: calc(24rpx + var(--safe-area-bottom));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: space-between;
  animation: slideUp 0.5s ease-out;
  min-height: 120rpx;
  gap: 20rpx;
}

.payment-floating {
  animation: slideUpFloat 0.5s ease-out;
}

.payment-pulse {
  position: relative;
  overflow: hidden;
}

.payment-pulse::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer-slide 2s infinite;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
  flex-shrink: 0;
  white-space: nowrap;
}

.price-symbol {
  font-size: 28rpx;
  color: var(--primary-color);
}

.price-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-color);
}

/* 加载状态动画 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 新增动画关键帧 */
@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20rpx) scale(1.1);
    opacity: 1;
  }
}

@keyframes flame {
  0% {
    transform: scale(1) rotate(0deg);
  }
  100% {
    transform: scale(1.1) rotate(5deg);
  }
}

@keyframes flicker {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  100% {
    opacity: 0.7;
    transform: scale(0.9);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10rpx);
  }
  60% {
    transform: translateY(-5rpx);
  }
}

@keyframes ring-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes badgePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(45, 140, 240, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10rpx rgba(45, 140, 240, 0);
  }
}

@keyframes slideUpFloat {
  0% {
    transform: translateY(100%);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes shimmer-slide {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes typewriter {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes iconGlow {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(165, 25, 30, 0.3);
  }
  50% {
    box-shadow: 0 0 0 10rpx rgba(165, 25, 30, 0);
  }
}

/* 费用明细样式 */
.fee-summary {
  margin: 32rpx var(--page-spacing);
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.fee-summary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.fee-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.01) 0%, transparent 50%);
  pointer-events: none;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  font-size: 28rpx;
  color: var(--text-secondary);
  position: relative;
  z-index: 1;
}

.fee-divider {
  height: 1rpx;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
  margin: 16rpx 0;
}

.fee-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  position: relative;
  z-index: 1;
}

.total-amount {
  font-size: 36rpx;
  font-weight: bold;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 现代化协议样式 */
.modern-agreement {
  margin: 32rpx var(--page-spacing);
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  position: relative;
  overflow: hidden;
}

.modern-agreement::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.02) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
}

.agreement-check {
  position: relative;
  z-index: 1;
  cursor: pointer;
}

.agreement-text {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: var(--text-color);
  position: relative;
  z-index: 1;
  text-align: center;
}

.agreement-link {
  color: var(--primary-color);
  text-decoration: underline;
  font-weight: 500;
}

.check-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(165, 25, 30, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.agreement-check:active .check-ripple {
  width: 60rpx;
  height: 60rpx;
}

/* 现代化增值服务样式 */
.modern-services {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.service-card {
  position: relative;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius-lg);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 16rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

/* 协议弹窗样式 */
.agreement-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

.agreement-popup-header {
  position: relative;
  background: linear-gradient(135deg, #a5191e 0%, #8b0000 100%);
  color: white;
  padding: 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(165, 25, 30, 0.3);
}

.agreement-popup-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.agreement-popup-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
}

.agreement-close {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.agreement-close:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.9);
}

.agreement-popup-content {
  flex: 1;
  padding: 24rpx;
}

.agreement-popup-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.popup-section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #a5191e;
  margin-bottom: 16rpx;
  padding-bottom: 8rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.popup-section-content {
  line-height: 1.6;
}

.popup-content-item {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  padding-left: 16rpx;
  position: relative;
}

.popup-content-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 12rpx;
  width: 6rpx;
  height: 6rpx;
  background: #a5191e;
  border-radius: 50%;
}

.popup-content-item:last-child {
  margin-bottom: 0;
}

.agreement-popup-contact {
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.05) 0%, rgba(255, 255, 255, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(165, 25, 30, 0.1);
}

.popup-contact-content {
  margin-top: 16rpx;
}

.popup-contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 12rpx;
  padding: 12rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8rpx;
}

.popup-contact-icon {
  font-size: 28rpx;
  width: 28rpx;
  text-align: center;
}

.popup-contact-text {
  font-size: 26rpx;
  color: #333;
  font-weight: 500;
}

.popup-work-time {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  margin-top: 12rpx;
  padding: 8rpx;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 6rpx;
}

.agreement-popup-footer {
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 16rpx;
}

.agreement-btn-secondary {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  border: none;
  border-radius: 10rpx;
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.agreement-btn-secondary:active {
  background: rgba(0, 0, 0, 0.1);
  transform: scale(0.98);
}

.agreement-btn-primary {
  flex: 2;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #a5191e 0%, #8b0000 100%);
  color: white;
  border: none;
  border-radius: 10rpx;
  font-size: 26rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(165, 25, 30, 0.3);
  transition: all 0.3s ease;
}

.agreement-btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(165, 25, 30, 0.4);
}

.service-card:active {
  transform: scale(0.98);
}

.service-card.selected {
  border-color: var(--primary-color);
  background: rgba(165, 25, 30, 0.05);
}

.service-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.01) 0%, transparent 50%);
  pointer-events: none;
}

.add-on-check {
  position: relative;
  z-index: 1;
  flex-shrink: 0;
}

.add-on-content {
  flex: 1;
  position: relative;
  z-index: 1;
}

.add-on-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: var(--text-color);
}

.add-on-price {
  color: var(--primary-color);
  font-weight: bold;
  font-size: 28rpx;
}

.add-on-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.service-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(165, 25, 30, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.service-card.selected .service-glow {
  opacity: 1;
}

/* 现代化健康声明样式 */
.modern-declaration {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.declaration-card {
  position: relative;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.declaration-card:active {
  transform: scale(0.98);
}

.declaration-card.selected {
  border-color: var(--primary-color);
  background: rgba(165, 25, 30, 0.05);
}

.declaration-card text {
  flex: 1;
  font-size: 26rpx;
  color: var(--text-color);
  line-height: 1.5;
  position: relative;
  z-index: 1;
}

.declaration-ripple {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(165, 25, 30, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  pointer-events: none;
}

.declaration-card:active .declaration-ripple {
  width: 100rpx;
  height: 100rpx;
}

.loading {
  background: linear-gradient(
    90deg,
    var(--border-color) 25%,
    var(--bg-color) 50%,
    var(--border-color) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.service-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.service-item:hover {
  background: rgba(45, 140, 240, 0.05);
  padding-left: 16rpx;
  padding-right: 16rpx;
  border-radius: 12rpx;
}

.service-premium {
  position: relative;
  background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
  border: 1rpx solid rgba(255, 107, 107, 0.2);
  border-radius: 12rpx;
  padding: 24rpx 16rpx;
}

.service-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, #ff6b6b, #ffa726);
  border-radius: 12rpx 12rpx 0 0;
}

/* 支付弹窗 - 美化样式 */
.payment-popup.modern-payment-popup {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding: 32rpx 24rpx 40rpx;
  box-shadow: 0 -6rpx 24rpx rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
  max-height: 80vh;
}

.payment-popup.modern-payment-popup::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3rpx;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

/* 支付弹窗头部 */
.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
  position: relative;
}

.payment-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 支付金额区域 */
.payment-amount {
  text-align: center;
  margin-bottom: 36rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.05), rgba(255, 143, 31, 0.05));
  border-radius: 16rpx;
  position: relative;
}

.payment-amount::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmerEffect 3s ease-in-out infinite;
}

.amount-text {
  font-size: 56rpx;
  font-weight: bold;
  letter-spacing: 1rpx;
  text-shadow: 0 2rpx 4rpx rgba(165, 25, 30, 0.15);
  position: relative;
  z-index: 1;
}

@keyframes shimmerEffect {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 支付方式选择区域 */
.payment-methods {
  margin-bottom: 36rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  padding: 24rpx 20rpx;
  margin-bottom: 16rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border: 2rpx solid rgba(165, 25, 30, 0.1);
  border-radius: 12rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.payment-method::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(165, 25, 30, 0.05), transparent);
  transition: left 0.5s ease;
}

.payment-method:active::before {
  left: 100%;
}

.payment-method:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(165, 25, 30, 0.15);
  border-color: var(--primary-color);
}

.payment-method.selected {
  border-color: var(--primary-color);
  background: linear-gradient(135deg, rgba(165, 25, 30, 0.05) 0%, rgba(255, 143, 31, 0.05) 100%);
  box-shadow: 0 4rpx 15rpx rgba(165, 25, 30, 0.1);
}

.payment-method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  border-radius: 8rpx;
}

.payment-method-info {
  flex: 1;
}

.payment-method-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 8rpx;
}

.payment-method-desc {
  font-size: 24rpx;
  color: var(--text-secondary);
}

.payment-method-radio {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid var(--border-color);
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.payment-method.selected .payment-method-radio {
  border-color: var(--primary-color);
  background: var(--primary-color);
}

.payment-method.selected .payment-method-radio::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16rpx;
  height: 16rpx;
  background: #fff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* 确认支付按钮 */
.confirm-payment-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: bold;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4rpx 16rpx rgba(165, 25, 30, 0.25);
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirm-payment-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.confirm-payment-btn:active::before {
  left: 100%;
}

.confirm-payment-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 15rpx rgba(165, 25, 30, 0.4);
}

.confirm-payment-btn:disabled {
  background: linear-gradient(135deg, #cccccc 0%, #999999 100%);
  box-shadow: none;
  transform: none;
}

.payment-method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.payment-method-name {
  flex: 1;
  font-size: 30rpx;
  color: var(--text-color);
}

.payment-method-radio .wx-radio-input {
  width: 36rpx;
  height: 36rpx;
  border-radius: 50%;
  border: 2rpx solid var(--border-color);
}

.payment-method-radio .wx-radio-input.wx-radio-input-checked {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
}

.payment-method-radio .wx-radio-input.wx-radio-input-checked::before {
  font-size: 28rpx; /* 调整对勾大小 */
  color: #fff;
}

.confirm-payment-btn {
  width: 100%;
  height: 90rpx;
  background-image: var(--primary-gradient);
  color: #fff;
  border-radius: 45rpx;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(165, 25, 30, 0.3);
  transition: transform 0.1s ease-out, box-shadow 0.1s ease-out;
}

.confirm-payment-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 10rpx rgba(165, 25, 30, 0.2);
}
