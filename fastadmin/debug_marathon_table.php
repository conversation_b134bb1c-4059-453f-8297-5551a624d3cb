<?php
// 调试马拉松表结构的脚本
require_once 'application/common.php';

use think\Db;

try {
    // 检查表是否存在
    $tables = Db::query("SHOW TABLES LIKE '%marathon_events%'");
    echo "马拉松相关表:\n";
    foreach ($tables as $table) {
        echo "- " . current($table) . "\n";
    }
    
    // 检查 marathon_events 表结构
    if (!empty($tables)) {
        $tableName = current($tables[0]);
        echo "\n表结构 ($tableName):\n";
        $columns = Db::query("DESCRIBE $tableName");
        foreach ($columns as $column) {
            echo "- {$column['Field']}: {$column['Type']} (默认: {$column['Default']})\n";
        }
        
        // 检查数据
        echo "\n表数据:\n";
        $data = Db::name('marathon_events')->limit(5)->select();
        foreach ($data as $row) {
            echo "ID: {$row['id']}, 名称: {$row['name']}, 状态: {$row['event_status']}\n";
        }
    }
    
    // 检查 events 表是否存在
    $eventsTables = Db::query("SHOW TABLES LIKE '%events%'");
    echo "\n所有events相关表:\n";
    foreach ($eventsTables as $table) {
        echo "- " . current($table) . "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
