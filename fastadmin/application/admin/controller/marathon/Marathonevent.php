<?php

namespace app\admin\controller\marathon;

use app\common\controller\Backend;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\Exception;

/**
 * 马拉松赛事管理
 *
 * @icon fa fa-road
 */
class Marathonevent extends Backend
{
    /**
     * Marathonevent模型对象
     * @var \app\admin\model\marathon\Marathonevent
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\marathon\Marathonevent;

        $statusList = $this->model->getStatusList();
        $eventStatusList = $this->model->getEventStatusList();

        // 调试信息
        \think\Log::write('Status List: ' . json_encode($statusList), 'info');
        \think\Log::write('Event Status List: ' . json_encode($eventStatusList), 'info');

        $this->view->assign("statusList", $statusList);
        $this->view->assign("eventStatusList", $eventStatusList);

        // 暂时禁用模型验证，避免验证器类找不到的问题
        $this->modelValidate = false;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 调试信息：记录原始提交的参数
                \think\Log::write('Original POST data: ' . json_encode($this->request->post()), 'info');
                \think\Log::write('Row params before exclude: ' . json_encode($params), 'info');
                \think\Log::write('Exclude fields: ' . json_encode($this->excludeFields), 'info');

                $params = $this->preExcludeFields($params);

                // 调试信息：记录过滤后的参数
                \think\Log::write('Marathon edit params after exclude: ' . json_encode($params), 'info');

                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\", ".", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    \think\Log::write('Marathon edit validation error: ' . $e->getMessage(), 'error');
                    $this->error('验证失败：' . $e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    \think\Log::write('Marathon edit PDO error: ' . $e->getMessage(), 'error');
                    $this->error('数据库错误：' . $e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    \think\Log::write('Marathon edit general error: ' . $e->getMessage(), 'error');
                    $this->error('系统错误：' . $e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 加载该赛事的比赛类型
        $raceOptionsModel = new \app\admin\model\marathon\MarathonRaceOptions;
        $raceOptions = $raceOptionsModel->where('event_id', $ids)->select();
        $this->view->assign('raceOptions', $raceOptions);
        
        // 获取原始数据以避免accessor转换问题
        // 使用DB类直接查询原始数据
        $originalData = \think\Db::name('events')->where('id', $ids)->find();
        $rowData = $row->toArray();
        if ($originalData) {
            // 确保所有字段都有值，避免undefined key错误
            foreach ($originalData as $key => $value) {
                if (!isset($rowData[$key])) {
                    $rowData[$key] = $value;
                }
            }
            // 设置默认值
            $rowData['highlights'] = $originalData['highlights'] ?? '';
            $rowData['gallery'] = $originalData['gallery'] ?? '';
        }

        // 调试信息：记录当前状态值
        \think\Log::write('Current row status: ' . ($rowData['status'] ?? 'null'), 'info');
        \think\Log::write('Row data: ' . json_encode($rowData), 'info');

        $this->view->assign("row", $rowData);
        return $this->view->fetch();
    }

    /**
     * 导入
     */
    public function import()
    {
        $file = $this->request->request('file');
        if (!$file) {
            $this->error(__('Parameter %s can not be empty', 'file'));
        }
        $filePath = ROOT_PATH . DS . 'public' . DS . $file;
        if (!is_file($filePath)) {
            $this->error(__('No results were found'));
        }

        $PHPReader = new \PHPExcel_Reader_Excel2007();
        if (!$PHPReader->canRead($filePath)) {
            $PHPReader = new \PHPExcel_Reader_Excel5();
            if (!$PHPReader->canRead($filePath)) {
                $PHPReader = new \PHPExcel_Reader_CSV();
                if (!$PHPReader->canRead($filePath)) {
                    $this->error(__('Unknown data format'));
                }
            }
        }

        $table = $PHPReader->load($filePath);
        $dataList = $table->getSheet(0)->toArray();
        if (isset($dataList[0])) {
            $fields = $dataList[0];
            array_shift($dataList);
            $insert = [];
            $update = [];
            foreach ($dataList as $row) {
                $temp = [];
                foreach ($fields as $key => $field) {
                    $temp[$field] = $row[$key];
                }
                if (!empty($temp['id'])) {
                    $update[] = $temp;
                } else {
                    $insert[] = $temp;
                }
            }
            if ($insert) {
                $this->model->saveAll($insert);
            }
            if ($update) {
                foreach ($update as $data) {
                    $this->model->update($data);
                }
            }
            $this->success();
        } else {
            $this->error(__('No results were found'));
        }
    }

    /**
     * 导出
     */
    public function export()
    {
        $list = $this->model->select();
        $result = array("total" => count($list), "rows" => $list);

        $this->success("导出成功", null, $result);
    }
}
