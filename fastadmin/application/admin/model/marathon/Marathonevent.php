<?php

namespace app\admin\model\marathon;

use think\Model;

class Marathonevent extends Model
{
    // 表名
    protected $name = 'events';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';

    // 追加属性
    protected $append = [
        'event_status_text',
        'status_text',
        'event_date_text',
        'registration_start_text',
        'registration_end_text',
        'banner_images_list',
        'current_registration_status'
    ];
    
    // JSON字段
    protected $json = ['banner_images'];
    
    /**
     * 获取赛事状态列表
     */
    public function getEventStatusList()
    {
        return [
            'registering' => '报名中',
            'upcoming' => '即将开始',
            'ongoing' => '进行中',
            'finished' => '已结束',
            'canceled' => '已取消'
        ];
    }

    /**
     * 获取状态列表
     */
    public function getStatusList()
    {
        return ['normal' => '正常', 'hidden' => '隐藏'];
    }

    /**
     * 获取赛事类型列表
     */
    public function getTypeList()
    {
        return [
            'marathon' => '全程马拉松',
            'half_marathon' => '半程马拉松',
            'mini_marathon' => '迷你马拉松',
            'fun_run' => '欢乐跑'
        ];
    }

    /**
     * 获取赛事状态文本
     */
    public function getEventStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['event_status']) ? $data['event_status'] : '');
        $list = $this->getEventStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    /**
     * 获取赛事日期文本
     */
    public function getEventDateTextAttr($value, $data)
    {
        $date = isset($data['event_date']) ? $data['event_date'] : '';
        return $date ? date('Y年m月d日', strtotime($date)) : '';
    }

    /**
     * 获取报名开始时间文本
     */
    public function getRegistrationStartTextAttr($value, $data)
    {
        $date = isset($data['registration_start']) ? $data['registration_start'] : '';
        return $date ? date('Y-m-d H:i', strtotime($date)) : '';
    }

    /**
     * 获取报名结束时间文本
     */
    public function getRegistrationEndTextAttr($value, $data)
    {
        $date = isset($data['registration_end']) ? $data['registration_end'] : '';
        return $date ? date('Y-m-d H:i', strtotime($date)) : '';
    }

    /**
     * 获取轮播图列表
     */
    public function getBannerImagesListAttr($value, $data)
    {
        $images = isset($data['banner_images']) ? $data['banner_images'] : [];
        if (is_string($images)) {
            $images = json_decode($images, true) ?: [];
        }
        return $images;
    }

    /**
     * 获取当前报名状态
     */
    public function getCurrentRegistrationStatusAttr($value, $data)
    {
        $now = time();
        $regStart = isset($data['registration_start']) ? strtotime($data['registration_start']) : 0;
        $regEnd = isset($data['registration_end']) ? strtotime($data['registration_end']) : 0;
        
        if ($now < $regStart) {
            return 'not_started';
        } elseif ($now > $regEnd) {
            return 'ended';
        } else {
            return 'ongoing';
        }
    }

    /**
     * 设置轮播图
     */
    public function setBannerImages($images)
    {
        $this->banner_images = is_array($images) ? $images : [];
        return $this;
    }
    
    /**
     * 设置赛事日期
     * @param string $value
     * @return string
     */
    public function setEventDateAttr($value)
    {
        return $value ? $value : null;
    }
    
    /**
     * 获取赛事日期
     * @param mixed $value
     * @return string
     */
    public function getEventDateAttr($value)
    {
        return $value ? $value : '';
    }
    
    /**
     * 设置报名开始时间
     * @param string $value
     * @return string
     */
    public function setRegistrationStartAttr($value)
    {
        return $value ? $value : null;
    }
    
    /**
     * 获取报名开始时间
     * @param string $value
     * @return string
     */
    public function getRegistrationStartAttr($value)
    {
        return $value ? $value : '';
    }

    /**
     * 获取报名截止时间
     * @param string $value
     * @return string
     */
    public function getRegistrationEndAttr($value)
    {
        return $value ? $value : '';
    }
    


    // 验证规则
    protected $rule = [
        'name' => 'require|length:1,255',
        'event_date' => 'require|date',
        'location' => 'require|length:1,255',
        'description' => 'length:0,65535',
        'status' => 'in:registering,upcoming,ongoing,finished,canceled',
        'registration_start' => 'require|date',
        'registration_end' => 'require|date',
        'is_hot' => 'in:0,1',
        'max_participants' => 'integer|egt:0',
        'current_participants' => 'integer|egt:0'
    ];
    
    // 时间字段
    protected $dateFormat = 'Y-m-d H:i:s';
    protected $type = [
        'event_date'         => 'datetime',
        'registration_start' => 'datetime',
        'registration_end'   => 'datetime',
        'createtime'         => 'timestamp',
        'updatetime'         => 'timestamp',
        'deletetime'         => 'timestamp',
        'is_hot'             => 'integer',
        'max_participants'   => 'integer',
        'current_participants' => 'integer',
        'sort'               => 'integer',
    ];
    
    // 允许字段
    protected $allowField = true;
    
    // 图片预览
    public function getImagePreviewAttr($value, $data)
    {
        if (!$data['image']) {
            return '';
        }
        return cdnurl($data['image'], true);
    }
    
    // 赛事亮点获取器
    public function getHighlightsAttr($value)
    {
        return $value ? json_decode($value, true) : [];
    }
    
    // 赛事亮点修改器
    public function setHighlightsAttr($value)
    {
        return is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
    }
    
    // 图片库获取器
    public function getGalleryAttr($value)
    {
        return $value ? explode(',', $value) : [];
    }
    
    // 图片库修改器
    public function setGalleryAttr($value)
    {
        return is_array($value) ? implode(',', $value) : $value;
    }
    
    // 是否热门文本
    public function getIsHotTextAttr($value, $data)
    {
        return isset($data['is_hot']) && $data['is_hot'] ? '是' : '否';
    }
    
    // 评分星级显示
    public function getRatingStarsAttr($value, $data)
    {
        $rating = isset($data['rating']) ? $data['rating'] : 5;
        return str_repeat('★', $rating) . str_repeat('☆', 5 - $rating);
    }

    protected static function init()
    {
        self::beforeInsert(function ($row) {
            $row['createtime'] = time();
            $row['updatetime'] = time();
        });

        self::beforeUpdate(function ($row) {
            $row['updatetime'] = time();
        });
    }

    /**
     * 关联比赛类型
     */
    public function raceTypes()
    {
        return $this->hasMany('MarathonRaceOptions', 'event_id');
    }
    
    /**
     * 关联赛事项目（兼容旧方法名）
     */
    public function raceoptions()
    {
        return $this->hasMany('Raceoption', 'event_id', 'id');
    }
}
