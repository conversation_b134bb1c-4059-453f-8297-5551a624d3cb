<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('赛事名称')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}" data-rule="required">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('比赛日期')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-event_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[event_date]" type="text" value="{$row.event_date}" data-rule="required">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('比赛地点')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-location" class="form-control" name="row[location]" type="text" value="{$row.location|htmlentities}" data-rule="required">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('封面图片')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" class="form-control" size="50" name="row[image]" type="text" value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('比赛类型管理')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> 请到 <strong>比赛类型管理</strong> 中为此赛事管理具体的比赛项目及对应的报名费用。
                <br><a href="{:url('marathon/marathon_race_options/index')}?event_id={$row.id}" target="_blank" class="btn btn-sm btn-primary" style="margin-top: 10px;">
                    <i class="fa fa-external-link"></i> 管理此赛事的比赛类型
                </a>
            </div>
            
            {if condition="$raceOptions"}
            <div class="panel panel-default" style="margin-top: 15px;">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <i class="fa fa-list"></i> 已添加的比赛类型
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered">
                            <thead>
                                   <tr>
                                       <th>比赛类型</th>
                                       <th>距离</th>
                                       <th>报名费</th>
                                       <th>操作</th>
                                   </tr>
                               </thead>
                               <tbody>
                                   {foreach name="raceOptions" item="option"}
                                   <tr>
                                       <td>{$option.name}</td>
                                       <td>{$option.distance}</td>
                                       <td>¥{$option.price}</td>
                                       <td>
                                           <a href="{:url('marathon/marathon_race_options/edit')}?ids={$option.id}" class="btn btn-xs btn-primary" target="_blank">
                                               <i class="fa fa-edit"></i> 编辑
                                           </a>
                                       </td>
                                   </tr>
                                   {/foreach}
                               </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {else /}
            <div class="alert alert-warning" style="margin-top: 15px;">
                <i class="fa fa-exclamation-triangle"></i> 暂未添加任何比赛类型，请点击上方按钮进行添加。
            </div>
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('赛事状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="eventStatusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__("倒计时天数")}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-countdown_days" class="form-control" name="row[countdown_days]" type="number" value="{$row.countdown_days|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__("当前报名人数")}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-current_participants" class="form-control" name="row[current_participants]" type="number" value="{$row.current_participants|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__("最大参赛人数")}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-max_participants" class="form-control" name="row[max_participants]" type="number" value="{$row.max_participants|htmlentities}" placeholder="0表示不限制">
        </div>
    </div>


    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('赛事描述')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-description" class="form-control editor" rows="5" name="row[description]" cols="50">{$row.description|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__("赛事亮点")}:</label>
        <div class="col-xs-12 col-sm-8">
            <div id="highlights-container">
                <!-- 动态生成的亮点项目将在这里显示 -->
            </div>
            <button type="button" class="btn btn-success btn-sm" id="add-highlight"><i class="fa fa-plus"></i> 添加亮点</button>
            <input type="hidden" name="row[highlights]" id="highlights-json" value="{$row.highlights|default=''|htmlentities}" />
            <div class="help-block">添加赛事的特色亮点，用于在前台展示</div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__("图片库")}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-gallery" class="form-control" name="row[gallery]" type="text" value="{$row.gallery|htmlentities}" placeholder="多张图片用逗号分隔">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-gallery" class="btn btn-danger faupload" data-input-id="c-gallery" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="true" data-preview-id="p-gallery"><i class="fa fa-upload"></i> {:__("Upload")}</button></span>
                    <span><button type="button" id="fachoose-gallery" class="btn btn-primary fachoose" data-input-id="c-gallery" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__("Choose")}</button></span>
                </div>
                <span class="msg-box n-right" for="c-gallery"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-gallery"></ul>
            <div class="help-block">上传赛事相关图片，用于前台展示</div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__("设为热门")}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label for="row[is_hot]-1"><input id="row[is_hot]-1" name="row[is_hot]" type="radio" value="1" {in name="row.is_hot" value="1"}checked{/in} /> {:__("是")}</label>
                <label for="row[is_hot]-0"><input id="row[is_hot]-0" name="row[is_hot]" type="radio" value="0" {in name="row.is_hot" value="0"}checked{/in} /> {:__("否")}</label>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('报名开始时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-registration_start" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[registration_start]" type="text" value="{$row.registration_start}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('报名截止时间')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-registration_end" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[registration_end]" type="text" value="{$row.registration_end}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('权重')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="{$row.weigh|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label>
            {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
